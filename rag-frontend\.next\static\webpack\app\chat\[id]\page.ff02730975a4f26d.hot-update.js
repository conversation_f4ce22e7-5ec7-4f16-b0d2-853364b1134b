"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./src/components/chat/ChatInput.tsx":
/*!*******************************************!*\
  !*** ./src/components/chat/ChatInput.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ChatInput = (param)=>{\n    let { onSend } = param;\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            onSend(e.currentTarget.value);\n            e.currentTarget.value = ''; // 清空输入框\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"chat-input-container flex items-center space-x-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                placeholder: \"输入消息...\",\n                rows: 2,\n                className: \"flex-grow border-none resize-none\",\n                onKeyPress: handleKeyPress\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                onClick: ()=>onSend(document.querySelector('textarea').value),\n                className: \"bg-blue-500 text-white px-4 py-2 rounded\",\n                children: \"发送\"\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: \"bg-gray-200 text-gray-600 px-4 py-2 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-6 w-6\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13v-2m0 4h8m-8 0h8m-9 8h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ChatInput;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInput);\nvar _c;\n$RefreshReg$(_c, \"ChatInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NoYXQvQ2hhdElucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUkwQjtBQU0xQixNQUFNQyxZQUFzQztRQUFDLEVBQUVDLE1BQU0sRUFBRTtJQUNyRCxNQUFNQyxpQkFBaUIsQ0FBQ0M7UUFDdEIsSUFBSUEsRUFBRUMsR0FBRyxLQUFLLFdBQVcsQ0FBQ0QsRUFBRUUsUUFBUSxFQUFFO1lBQ3BDRixFQUFFRyxjQUFjO1lBQ2hCTCxPQUFPRSxFQUFFSSxhQUFhLENBQUNDLEtBQUs7WUFDNUJMLEVBQUVJLGFBQWEsQ0FBQ0MsS0FBSyxHQUFHLElBQUksUUFBUTtRQUN0QztJQUNGO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFDQ0MsYUFBWTtnQkFDWkMsTUFBTTtnQkFDTkgsV0FBVTtnQkFDVkksWUFBWVo7Ozs7OzswQkFFZCw4REFBQ2E7Z0JBQU9DLE1BQUs7Z0JBQVNDLFNBQVMsSUFBTWhCLE9BQU9pQixTQUFTQyxhQUFhLENBQUMsWUFBWVgsS0FBSztnQkFBR0UsV0FBVTswQkFBMkM7Ozs7OzswQkFHNUksOERBQUNLO2dCQUFPQyxNQUFLO2dCQUFTTixXQUFVOzBCQUU5Qiw0RUFBQ1U7b0JBQUlDLE9BQU07b0JBQTZCWCxXQUFVO29CQUFVWSxNQUFLO29CQUFPQyxTQUFRO29CQUFZQyxRQUFPOzhCQUNqRyw0RUFBQ0M7d0JBQUtDLGVBQWM7d0JBQVFDLGdCQUFlO3dCQUFRQyxhQUFhO3dCQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSy9FO0tBNUJNN0I7QUE4Qk4saUVBQWVBLFNBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxkYXRhXFxzdHVkeVxccHl0aG9uXFxyYWdcXHJhZy1mcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxjaGF0XFxDaGF0SW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VSZWYsIEtleWJvYXJkRXZlbnQgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IFNlbmQsIFBhcGVyY2xpcCwgTWljIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcclxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuXHJcbmludGVyZmFjZSBDaGF0SW5wdXRQcm9wcyB7XHJcbiAgb25TZW5kOiAobWVzc2FnZTogc3RyaW5nKSA9PiB2b2lkO1xyXG59XHJcblxyXG5jb25zdCBDaGF0SW5wdXQ6IFJlYWN0LkZDPENoYXRJbnB1dFByb3BzPiA9ICh7IG9uU2VuZCB9KSA9PiB7XHJcbiAgY29uc3QgaGFuZGxlS2V5UHJlc3MgPSAoZTogUmVhY3QuS2V5Ym9hcmRFdmVudDxIVE1MVGV4dEFyZWFFbGVtZW50PikgPT4ge1xyXG4gICAgaWYgKGUua2V5ID09PSAnRW50ZXInICYmICFlLnNoaWZ0S2V5KSB7XHJcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuICAgICAgb25TZW5kKGUuY3VycmVudFRhcmdldC52YWx1ZSk7XHJcbiAgICAgIGUuY3VycmVudFRhcmdldC52YWx1ZSA9ICcnOyAvLyDmuIXnqbrovpPlhaXmoYZcclxuICAgIH1cclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJjaGF0LWlucHV0LWNvbnRhaW5lciBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cclxuICAgICAgPHRleHRhcmVhIFxyXG4gICAgICAgIHBsYWNlaG9sZGVyPVwi6L6T5YWl5raI5oGvLi4uXCJcclxuICAgICAgICByb3dzPXsyfVxyXG4gICAgICAgIGNsYXNzTmFtZT1cImZsZXgtZ3JvdyBib3JkZXItbm9uZSByZXNpemUtbm9uZVwiXHJcbiAgICAgICAgb25LZXlQcmVzcz17aGFuZGxlS2V5UHJlc3N9XHJcbiAgICAgIC8+XHJcbiAgICAgIDxidXR0b24gdHlwZT1cImJ1dHRvblwiIG9uQ2xpY2s9eygpID0+IG9uU2VuZChkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCd0ZXh0YXJlYScpLnZhbHVlKX0gY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZFwiPlxyXG4gICAgICAgIOWPkemAgVxyXG4gICAgICA8L2J1dHRvbj5cclxuICAgICAgPGJ1dHRvbiB0eXBlPVwiYnV0dG9uXCIgY2xhc3NOYW1lPVwiYmctZ3JheS0yMDAgdGV4dC1ncmF5LTYwMCBweC00IHB5LTIgcm91bmRlZFwiPlxyXG4gICAgICAgIHsvKiDlgYforr7mnInkuIDkuKror63pn7PovpPlhaXlm77moIcgKi99XHJcbiAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwiaC02IHctNlwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxyXG4gICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTMgM2gybC40IDJNNyAxM2gxMGw0LThINS40TTcgMTNMNS40IDVNNyAxM3YtMm0wIDRoOG0tOCAwaDhtLTkgOGgxNGEyIDIgMCAwMDItMlY4YTIgMiAwIDAwLTItMkg1YTIgMiAwIDAwLTIgMnY4YTIgMiAwIDAwMiAyelwiIC8+XHJcbiAgICAgICAgPC9zdmc+XHJcbiAgICAgIDwvYnV0dG9uPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IENoYXRJbnB1dDtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQ2hhdElucHV0Iiwib25TZW5kIiwiaGFuZGxlS2V5UHJlc3MiLCJlIiwia2V5Iiwic2hpZnRLZXkiLCJwcmV2ZW50RGVmYXVsdCIsImN1cnJlbnRUYXJnZXQiLCJ2YWx1ZSIsImRpdiIsImNsYXNzTmFtZSIsInRleHRhcmVhIiwicGxhY2Vob2xkZXIiLCJyb3dzIiwib25LZXlQcmVzcyIsImJ1dHRvbiIsInR5cGUiLCJvbkNsaWNrIiwiZG9jdW1lbnQiLCJxdWVyeVNlbGVjdG9yIiwic3ZnIiwieG1sbnMiLCJmaWxsIiwidmlld0JveCIsInN0cm9rZSIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/ChatInput.tsx\n"));

/***/ })

});