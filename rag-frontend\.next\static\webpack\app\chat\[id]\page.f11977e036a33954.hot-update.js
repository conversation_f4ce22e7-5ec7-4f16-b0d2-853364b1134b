"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./src/app/chat/[id]/page.tsx":
/*!************************************!*\
  !*** ./src/app/chat/[id]/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_chat_ChatMessages__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/ChatMessages */ \"(app-pages-browser)/./src/components/chat/ChatMessages.tsx\");\n/* harmony import */ var _components_chat_ChatInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/chat/ChatInput */ \"(app-pages-browser)/./src/components/chat/ChatInput.tsx\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./src/lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ChatPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const conversationId = params.id;\n    const [conversation, setConversation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const { setConversationTitle } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_6__.useChatStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            if (!conversationId) return;\n            const loadConversationAndMessages = {\n                \"ChatPage.useEffect.loadConversationAndMessages\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // 通过历史接口同时获取对话信息和消息\n                        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.chatApi.getConversationHistory(conversationId);\n                        if (response.success && response.data) {\n                            setConversation(response.data.conversation);\n                            setConversationTitle(response.data.conversation.title);\n                            // 将qa_pairs转换为Message格式\n                            const messages = [];\n                            response.data.qa_pairs.forEach({\n                                \"ChatPage.useEffect.loadConversationAndMessages\": (qa)=>{\n                                    // 添加用户消息\n                                    messages.push({\n                                        id: qa.id + '_user',\n                                        content: qa.query,\n                                        role: 'user',\n                                        timestamp: qa.created_at,\n                                        conversationId: conversationId\n                                    });\n                                    // 添加助手回复\n                                    messages.push({\n                                        id: qa.id + '_assistant',\n                                        content: qa.answer,\n                                        role: 'assistant',\n                                        timestamp: qa.created_at,\n                                        conversationId: conversationId,\n                                        metadata: qa.metadata\n                                    });\n                                }\n                            }[\"ChatPage.useEffect.loadConversationAndMessages\"]);\n                            // 按时间排序消息\n                            messages.sort({\n                                \"ChatPage.useEffect.loadConversationAndMessages\": (a, b)=>{\n                                    const timeA = typeof a.timestamp === 'string' ? new Date(a.timestamp) : a.timestamp;\n                                    const timeB = typeof b.timestamp === 'string' ? new Date(b.timestamp) : b.timestamp;\n                                    return timeA.getTime() - timeB.getTime();\n                                }\n                            }[\"ChatPage.useEffect.loadConversationAndMessages\"]);\n                            setMessages(messages);\n                        }\n                    } catch (error) {\n                        console.error('加载对话失败:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ChatPage.useEffect.loadConversationAndMessages\"];\n            loadConversationAndMessages();\n            return ({\n                \"ChatPage.useEffect\": ()=>{\n                    setConversationTitle(null);\n                }\n            })[\"ChatPage.useEffect\"];\n        }\n    }[\"ChatPage.useEffect\"], [\n        conversationId,\n        setConversationTitle\n    ]);\n    // 处理初始消息（从sessionStorage获取）\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            if (conversation && messages.length === 0) {\n                const initialMessage = sessionStorage.getItem(\"initialMessage_\".concat(conversationId));\n                if (initialMessage) {\n                    // 发送初始消息\n                    handleSendMessage(initialMessage).then({\n                        \"ChatPage.useEffect\": ()=>{\n                            sessionStorage.removeItem(\"initialMessage_\".concat(conversationId));\n                        }\n                    }[\"ChatPage.useEffect\"]);\n                }\n            }\n        }\n    }[\"ChatPage.useEffect\"], [\n        conversation,\n        messages.length,\n        conversationId\n    ]);\n    const handleSendMessage = async (message)=>{\n        if (!message.trim() || !conversation) return;\n        // 添加用户消息到界面\n        const userMessage = {\n            id: Date.now().toString(),\n            content: message,\n            role: 'user',\n            timestamp: new Date(),\n            conversationId: conversation.id\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        // 创建一个临时的助手消息用于流式更新\n        const tempAssistantId = 'temp_' + Date.now();\n        const assistantMessage = {\n            id: tempAssistantId,\n            content: '',\n            role: 'assistant',\n            timestamp: new Date(),\n            conversationId: conversation.id\n        };\n        setMessages((prev)=>[\n                ...prev,\n                assistantMessage\n            ]);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_3__.chatApi.sendMessageStream(conversationId, {\n                message: message\n            }, (chunk)=>{\n                switch(chunk.type){\n                    case 'start':\n                        break;\n                    case 'content':\n                        // 更新助手消息内容（直接替换，因为后端返回的是完整文本）\n                        if (chunk.content) {\n                            setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantId ? {\n                                        ...msg,\n                                        content: chunk.content || ''\n                                    } : msg));\n                        }\n                        break;\n                    case 'done':\n                        // 流式完成，更新消息ID和元数据\n                        setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantId ? {\n                                    ...msg,\n                                    id: chunk.qa_pair_id || tempAssistantId,\n                                    metadata: chunk.metadata\n                                } : msg));\n                        break;\n                    case 'error':\n                        // 处理错误\n                        console.error('流式响应错误:', chunk.error);\n                        setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantId ? {\n                                    ...msg,\n                                    content: '抱歉，发生了错误：' + chunk.error\n                                } : msg));\n                        break;\n                }\n            });\n        } catch (error) {\n            console.error('发送消息失败:', error);\n            // 更新错误消息\n            setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantId ? {\n                        ...msg,\n                        content: '抱歉，发送消息失败，请重试。'\n                    } : msg));\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\",\n                        style: {\n                            animationDelay: '0.1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\",\n                        style: {\n                            animationDelay: '0.2s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this);\n    }\n    if (!conversation) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"对话不存在\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"请检查链接是否正确\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 187,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 max-w-3xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatMessages__WEBPACK_IMPORTED_MODULE_4__.ChatMessages, {\n                    messages: messages\n                }, void 0, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sticky bottom-0 bg-white/90 backdrop-blur-xl border-t border-gray-100/50 px-6 py-6 flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-3xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatInput__WEBPACK_IMPORTED_MODULE_5__.ChatInput, {\n                        onSend: handleSendMessage\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"UUWXrptroQlkUPQc5ieI+qMyeEk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams,\n        _lib_store__WEBPACK_IMPORTED_MODULE_6__.useChatStore\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/[id]/page.tsx\n"));

/***/ })

});