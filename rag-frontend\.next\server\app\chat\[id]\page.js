/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/chat/[id]/page";
exports.ids = ["app/chat/[id]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2F%5Bid%5D%2Fpage&page=%2Fchat%2F%5Bid%5D%2Fpage&appPaths=%2Fchat%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fchat%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cdata%5Cstudy%5Cpython%5Crag%5Crag-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdata%5Cstudy%5Cpython%5Crag%5Crag-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2F%5Bid%5D%2Fpage&page=%2Fchat%2F%5Bid%5D%2Fpage&appPaths=%2Fchat%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fchat%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cdata%5Cstudy%5Cpython%5Crag%5Crag-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdata%5Cstudy%5Cpython%5Crag%5Crag-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?ed9d\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/layout.tsx */ \"(rsc)/./src/app/chat/layout.tsx\"));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/[id]/page.tsx */ \"(rsc)/./src/app/chat/[id]/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'chat',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/chat/[id]/page\",\n        pathname: \"/chat/[id]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/chat/[id]/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjZfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZjaGF0JTJGJTVCaWQlNUQlMkZwYWdlJnBhZ2U9JTJGY2hhdCUyRiU1QmlkJTVEJTJGcGFnZSZhcHBQYXRocz0lMkZjaGF0JTJGJTVCaWQlNUQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGY2hhdCUyRiU1QmlkJTVEJTJGcGFnZS50c3gmYXBwRGlyPUQlM0ElNUNkYXRhJTVDc3R1ZHklNUNweXRob24lNUNyYWclNUNyYWctZnJvbnRlbmQlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUQlM0ElNUNkYXRhJTVDc3R1ZHklNUNweXRob24lNUNyYWclNUNyYWctZnJvbnRlbmQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QmaXNHbG9iYWxOb3RGb3VuZEVuYWJsZWQ9ISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQkFBc0Isb0pBQXFHO0FBQzNILHNCQUFzQixrVEFBd0Y7QUFDOUcsc0JBQXNCLDRTQUFxRjtBQUMzRyxzQkFBc0IsNFNBQXFGO0FBQzNHLHNCQUFzQixrVEFBd0Y7QUFDOUcsc0JBQXNCLDhKQUEyRztBQUNqSSxvQkFBb0Isb0tBQStHO0FBR2pJO0FBR0E7QUFDMkU7QUFDTDtBQUNUO0FBQ087QUFDTztBQUNPO0FBQ1A7QUFDSztBQUNZO0FBQ1c7QUFDeEI7QUFDRjtBQUNhO0FBQ2lFO0FBQ2hGO0FBQ1g7QUFDUTtBQUNoQjtBQUN1QjtBQUNQO0FBQ1Q7QUFDaUI7QUFDbEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLGlrQkFBNlA7QUFDalM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsaWtCQUE2UDtBQUNqUztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUdyQjtBQUNxQjtBQUN2Qiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNvRjtBQUdwRjtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsYUFBYSxPQUFvQyxJQUFJLENBQUU7QUFDdkQsZ0JBQWdCLE1BQXVDO0FBQ3ZELENBQUM7QUFDTTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLEtBQXFCLEVBQUUsRUFFMUIsQ0FBQztBQUNOO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixPQUF3QztBQUN2RSw2QkFBNkIsNkVBQWM7QUFDM0M7QUFDQSx3QkFBd0IsNkVBQWM7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVkscVNBQXFTO0FBQ2pUO0FBQ0EsOEJBQThCLDhGQUFnQjtBQUM5QyxVQUFVLHVCQUF1QjtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixxRkFBVTtBQUM5QixzQkFBc0IsMEZBQWdCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyw2RUFBYyxxREFBcUQsd0dBQTJCO0FBQ2pJO0FBQ0EseUJBQXlCLDZFQUFjLDZDQUE2Qyx1RkFBVTtBQUM5RixtQ0FBbUMsMkdBQXlCO0FBQzVEO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QiwyRkFBb0I7QUFDbEQ7QUFDQTtBQUNBLHFDQUFxQyxNQUE0RyxJQUFJLENBQWU7QUFDcEs7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQSxrQ0FBa0MsNkVBQWM7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQsc0dBQTRCO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLG9FQUFTO0FBQ3BCO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsNkdBQThCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixnR0FBcUI7QUFDbEQ7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQSxtQkFBbUIsNEVBQVM7QUFDNUI7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLDRFQUFlO0FBQy9DLGdDQUFnQyw2RUFBZ0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLElBQXNDO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSxnRkFBYztBQUMvRSwrREFBK0QseUNBQXlDO0FBQ3hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLFFBQVEsRUFBRSxNQUFNO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0Esa0JBQWtCO0FBQ2xCLHVDQUF1QyxRQUFRLEVBQUUsUUFBUTtBQUN6RDtBQUNBLGFBQWE7QUFDYjtBQUNBLGtDQUFrQyxzQ0FBc0M7QUFDeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLDBDQUEwQyw2RUFBYztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQztBQUNsQztBQUNBLCtCQUErQiwyRkFBYztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyw2RUFBYztBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsSUFBSTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckIsNENBQTRDO0FBQzVDO0FBQ0EseUJBQXlCLDZFQUFjO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFdBQVc7QUFDL0Isb0JBQW9CLDBCQUEwQjtBQUM5QyxtQ0FBbUM7QUFDbkM7QUFDQSx3QkFBd0IsNEVBQXNCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhHQUE4RyxpQkFBaUIsRUFBRSxvRkFBb0YsOEJBQThCLE9BQU87QUFDMVA7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsNkVBQWU7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyx1REFBdUQ7QUFDbEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0IsMkVBQWtCO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLGlFQUFZLGNBQWMsZ0ZBQUs7QUFDaEUsK0JBQStCLGlFQUFZO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMERBQTBELGlFQUFZO0FBQ3RFLCtCQUErQixpRUFBWTtBQUMzQztBQUNBLGlEQUFpRCxpRUFBWTtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLGlFQUFZO0FBQzdDLDhCQUE4Qiw2RkFBZTtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsa0VBQVM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVFQUF1RSxnR0FBc0I7QUFDN0YsNkJBQTZCO0FBQzdCO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLDhCQUE4Qiw2RUFBZTtBQUM3Qyw4QkFBOEIsdUVBQVk7QUFDMUMsb0NBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrRUFBK0UsNkVBQWMsd0RBQXdELGdHQUFzQjtBQUMzSztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckIsMkJBQTJCLGtFQUFTO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EsdUdBQXVHLDZFQUFlO0FBQ3RIO0FBQ0EsaUhBQWlILG1GQUFtRjtBQUNwTTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIscUdBQXdCO0FBQ3REO0FBQ0Esb0JBQW9CLG9CQUFvQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0hBQWdILG9DQUFvQztBQUNwSjtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBLHdDQUF3QyxvRUFBYztBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpSEFBaUgsNkVBQWU7QUFDaEk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIscUdBQXdCO0FBQ3REO0FBQ0E7QUFDQSxpSEFBaUgsNEVBQXNCO0FBQ3ZJO0FBQ0Esa0NBQWtDLDRFQUFzQjtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixnRkFBZ0I7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyx1RUFBWTtBQUM1QztBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGdGQUFnQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHVFQUFZO0FBQ3hDO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyw2RUFBYztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIseUJBQXlCLDZFQUFjO0FBQ3ZDLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLDRFQUFzQjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJHQUEyRyw0RUFBc0I7QUFDakk7QUFDQSw4QkFBOEIsNEVBQXNCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQsaUdBQWtCO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHFHQUF3QjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBLDJCQUEyQixnRkFBZ0I7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQixxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGdGQUFnQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHVFQUFZO0FBQ3hDO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGdGQUFnQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxxRkFBWTtBQUN2RDtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLHVCQUF1QixnRkFBZ0I7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLCtGQUErRiw2RUFBZTtBQUM5RztBQUNBLHNHQUFzRyx1RUFBdUU7QUFDN0s7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2IsbUJBQW1CLGdGQUFnQjtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWLG9GQUFvRixnRkFBYztBQUNsRyxpQ0FBaUMsUUFBUSxFQUFFLFFBQVE7QUFDbkQsMEJBQTBCLHVFQUFRO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsNENBQTRDLDZGQUFlO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLDJGQUFtQjtBQUNyRDtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbW9kdWxlMCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcZGF0YVxcXFxzdHVkeVxcXFxweXRob25cXFxccmFnXFxcXHJhZy1mcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG5jb25zdCBtb2R1bGUxID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9nbG9iYWwtZXJyb3IuanNcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9ub3QtZm91bmQuanNcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9mb3JiaWRkZW4uanNcIik7XG5jb25zdCBtb2R1bGU0ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi91bmF1dGhvcml6ZWQuanNcIik7XG5jb25zdCBtb2R1bGU1ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxkYXRhXFxcXHN0dWR5XFxcXHB5dGhvblxcXFxyYWdcXFxccmFnLWZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcY2hhdFxcXFxsYXlvdXQudHN4XCIpO1xuY29uc3QgcGFnZTYgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGRhdGFcXFxcc3R1ZHlcXFxccHl0aG9uXFxcXHJhZ1xcXFxyYWctZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxjaGF0XFxcXFtpZF1cXFxccGFnZS50c3hcIik7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNzcidcbn07XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuaW1wb3J0IHsgZ2V0UmV2YWxpZGF0ZVJlYXNvbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2luc3RydW1lbnRhdGlvbi91dGlsc1wiO1xuaW1wb3J0IHsgZ2V0VHJhY2VyLCBTcGFuS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi90cmFjZS90cmFjZXJcIjtcbmltcG9ydCB7IGdldFJlcXVlc3RNZXRhIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVxdWVzdC1tZXRhXCI7XG5pbXBvcnQgeyBCYXNlU2VydmVyU3BhbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi90cmFjZS9jb25zdGFudHNcIjtcbmltcG9ydCB7IGludGVyb3BEZWZhdWx0IH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9pbnRlcm9wLWRlZmF1bHRcIjtcbmltcG9ydCB7IE5vZGVOZXh0UmVxdWVzdCwgTm9kZU5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Jhc2UtaHR0cC9ub2RlXCI7XG5pbXBvcnQgeyBjaGVja0lzQXBwUFBSRW5hYmxlZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9leHBlcmltZW50YWwvcHByXCI7XG5pbXBvcnQgeyBnZXRGYWxsYmFja1JvdXRlUGFyYW1zIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVxdWVzdC9mYWxsYmFjay1wYXJhbXNcIjtcbmltcG9ydCB7IHNldFJlZmVyZW5jZU1hbmlmZXN0c1NpbmdsZXRvbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW5jcnlwdGlvbi11dGlsc1wiO1xuaW1wb3J0IHsgaXNIdG1sQm90UmVxdWVzdCwgc2hvdWxkU2VydmVTdHJlYW1pbmdNZXRhZGF0YSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9zdHJlYW1pbmctbWV0YWRhdGFcIjtcbmltcG9ydCB7IGNyZWF0ZVNlcnZlck1vZHVsZU1hcCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvYWN0aW9uLXV0aWxzXCI7XG5pbXBvcnQgeyBub3JtYWxpemVBcHBQYXRoIH0gZnJvbSBcIm5leHQvZGlzdC9zaGFyZWQvbGliL3JvdXRlci91dGlscy9hcHAtcGF0aHNcIjtcbmltcG9ydCB7IGdldElzUG9zc2libGVTZXJ2ZXJBY3Rpb24gfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvc2VydmVyLWFjdGlvbi1yZXF1ZXN0LW1ldGFcIjtcbmltcG9ydCB7IFJTQ19IRUFERVIsIE5FWFRfUk9VVEVSX1BSRUZFVENIX0hFQURFUiwgTkVYVF9JU19QUkVSRU5ERVJfSEVBREVSLCBORVhUX0RJRF9QT1NUUE9ORV9IRUFERVIgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2FwcC1yb3V0ZXItaGVhZGVyc1wiO1xuaW1wb3J0IHsgZ2V0Qm90VHlwZSwgaXNCb3QgfSBmcm9tIFwibmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2lzLWJvdFwiO1xuaW1wb3J0IHsgQ2FjaGVkUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVzcG9uc2UtY2FjaGVcIjtcbmltcG9ydCB7IEZhbGxiYWNrTW9kZSwgcGFyc2VGYWxsYmFja0ZpZWxkIH0gZnJvbSBcIm5leHQvZGlzdC9saWIvZmFsbGJhY2tcIjtcbmltcG9ydCBSZW5kZXJSZXN1bHQgZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVuZGVyLXJlc3VsdFwiO1xuaW1wb3J0IHsgQ0FDSEVfT05FX1lFQVIsIE5FWFRfQ0FDSEVfVEFHU19IRUFERVIgfSBmcm9tIFwibmV4dC9kaXN0L2xpYi9jb25zdGFudHNcIjtcbmltcG9ydCB7IEVOQ09ERURfVEFHUyB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3N0cmVhbS11dGlscy9lbmNvZGVkLXRhZ3NcIjtcbmltcG9ydCB7IHNlbmRSZW5kZXJSZXN1bHQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9zZW5kLXBheWxvYWRcIjtcbmltcG9ydCB7IE5vRmFsbGJhY2tFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3Qvc2hhcmVkL2xpYi9uby1mYWxsYmFjay1lcnJvci5leHRlcm5hbFwiO1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ2NoYXQnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdbaWRdJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogW3BhZ2U2LCBcIkQ6XFxcXGRhdGFcXFxcc3R1ZHlcXFxccHl0aG9uXFxcXHJhZ1xcXFxyYWctZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxjaGF0XFxcXFtpZF1cXFxccGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFttb2R1bGU1LCBcIkQ6XFxcXGRhdGFcXFxcc3R1ZHlcXFxccHl0aG9uXFxcXHJhZ1xcXFxyYWctZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxjaGF0XFxcXGxheW91dC50c3hcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFEOlxcXFxkYXRhXFxcXHN0dWR5XFxcXHB5dGhvblxcXFxyYWdcXFxccmFnLWZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbbW9kdWxlMCwgXCJEOlxcXFxkYXRhXFxcXHN0dWR5XFxcXHB5dGhvblxcXFxyYWdcXFxccmFnLWZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidnbG9iYWwtZXJyb3InOiBbbW9kdWxlMSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9nbG9iYWwtZXJyb3IuanNcIl0sXG4nbm90LWZvdW5kJzogW21vZHVsZTIsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2J1aWx0aW4vbm90LWZvdW5kLmpzXCJdLFxuJ2ZvcmJpZGRlbic6IFttb2R1bGUzLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9idWlsdGluL2ZvcmJpZGRlbi5qc1wiXSxcbid1bmF1dGhvcml6ZWQnOiBbbW9kdWxlNCwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi91bmF1dGhvcml6ZWQuanNcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFEOlxcXFxkYXRhXFxcXHN0dWR5XFxcXHB5dGhvblxcXFxyYWdcXFxccmFnLWZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJEOlxcXFxkYXRhXFxcXHN0dWR5XFxcXHB5dGhvblxcXFxyYWdcXFxccmFnLWZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcY2hhdFxcXFxbaWRdXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmltcG9ydCBHbG9iYWxFcnJvciBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2J1aWx0aW4vZ2xvYmFsLWVycm9yLmpzXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuZXhwb3J0IHsgR2xvYmFsRXJyb3IgfTtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuaW1wb3J0ICogYXMgZW50cnlCYXNlIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbmltcG9ydCB7IFJlZGlyZWN0U3RhdHVzQ29kZSB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVkaXJlY3Qtc3RhdHVzLWNvZGVcIjtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL2NoYXQvW2lkXS9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9jaGF0L1tpZF1cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9LFxuICAgIGRpc3REaXI6IHByb2Nlc3MuZW52Ll9fTkVYVF9SRUxBVElWRV9ESVNUX0RJUiB8fCAnJyxcbiAgICBwcm9qZWN0RGlyOiBwcm9jZXNzLmVudi5fX05FWFRfUkVMQVRJVkVfUFJPSkVDVF9ESVIgfHwgJydcbn0pO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGhhbmRsZXIocmVxLCByZXMsIGN0eCkge1xuICAgIHZhciBfdGhpcztcbiAgICBsZXQgc3JjUGFnZSA9IFwiL2NoYXQvW2lkXS9wYWdlXCI7XG4gICAgLy8gdHVyYm9wYWNrIGRvZXNuJ3Qgbm9ybWFsaXplIGAvaW5kZXhgIGluIHRoZSBwYWdlIG5hbWVcbiAgICAvLyBzbyB3ZSBuZWVkIHRvIHRvIHByb2Nlc3MgZHluYW1pYyByb3V0ZXMgcHJvcGVybHlcbiAgICAvLyBUT0RPOiBmaXggdHVyYm9wYWNrIHByb3ZpZGluZyBkaWZmZXJpbmcgdmFsdWUgZnJvbSB3ZWJwYWNrXG4gICAgaWYgKHByb2Nlc3MuZW52LlRVUkJPUEFDSykge1xuICAgICAgICBzcmNQYWdlID0gc3JjUGFnZS5yZXBsYWNlKC9cXC9pbmRleCQvLCAnJykgfHwgJy8nO1xuICAgIH0gZWxzZSBpZiAoc3JjUGFnZSA9PT0gJy9pbmRleCcpIHtcbiAgICAgICAgLy8gd2UgYWx3YXlzIG5vcm1hbGl6ZSAvaW5kZXggc3BlY2lmaWNhbGx5XG4gICAgICAgIHNyY1BhZ2UgPSAnLyc7XG4gICAgfVxuICAgIGNvbnN0IG11bHRpWm9uZURyYWZ0TW9kZSA9IHByb2Nlc3MuZW52Ll9fTkVYVF9NVUxUSV9aT05FX0RSQUZUX01PREU7XG4gICAgY29uc3QgaW5pdGlhbFBvc3Rwb25lZCA9IGdldFJlcXVlc3RNZXRhKHJlcSwgJ3Bvc3Rwb25lZCcpO1xuICAgIC8vIFRPRE86IHJlcGxhY2Ugd2l0aCBtb3JlIHNwZWNpZmljIGZsYWdzXG4gICAgY29uc3QgbWluaW1hbE1vZGUgPSBnZXRSZXF1ZXN0TWV0YShyZXEsICdtaW5pbWFsTW9kZScpO1xuICAgIGNvbnN0IHByZXBhcmVSZXN1bHQgPSBhd2FpdCByb3V0ZU1vZHVsZS5wcmVwYXJlKHJlcSwgcmVzLCB7XG4gICAgICAgIHNyY1BhZ2UsXG4gICAgICAgIG11bHRpWm9uZURyYWZ0TW9kZVxuICAgIH0pO1xuICAgIGlmICghcHJlcGFyZVJlc3VsdCkge1xuICAgICAgICByZXMuc3RhdHVzQ29kZSA9IDQwMDtcbiAgICAgICAgcmVzLmVuZCgnQmFkIFJlcXVlc3QnKTtcbiAgICAgICAgY3R4LndhaXRVbnRpbCA9PSBudWxsID8gdm9pZCAwIDogY3R4LndhaXRVbnRpbC5jYWxsKGN0eCwgUHJvbWlzZS5yZXNvbHZlKCkpO1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgY29uc3QgeyBidWlsZElkLCBxdWVyeSwgcGFyYW1zLCBwYXJzZWRVcmwsIHBhZ2VJc0R5bmFtaWMsIGJ1aWxkTWFuaWZlc3QsIG5leHRGb250TWFuaWZlc3QsIHJlYWN0TG9hZGFibGVNYW5pZmVzdCwgc2VydmVyQWN0aW9uc01hbmlmZXN0LCBjbGllbnRSZWZlcmVuY2VNYW5pZmVzdCwgc3VicmVzb3VyY2VJbnRlZ3JpdHlNYW5pZmVzdCwgcHJlcmVuZGVyTWFuaWZlc3QsIGlzRHJhZnRNb2RlLCByZXNvbHZlZFBhdGhuYW1lLCByZXZhbGlkYXRlT25seUdlbmVyYXRlZCwgcm91dGVyU2VydmVyQ29udGV4dCwgbmV4dENvbmZpZyB9ID0gcHJlcGFyZVJlc3VsdDtcbiAgICBjb25zdCBwYXRobmFtZSA9IHBhcnNlZFVybC5wYXRobmFtZSB8fCAnLyc7XG4gICAgY29uc3Qgbm9ybWFsaXplZFNyY1BhZ2UgPSBub3JtYWxpemVBcHBQYXRoKHNyY1BhZ2UpO1xuICAgIGxldCB7IGlzT25EZW1hbmRSZXZhbGlkYXRlIH0gPSBwcmVwYXJlUmVzdWx0O1xuICAgIGNvbnN0IHByZXJlbmRlckluZm8gPSBwcmVyZW5kZXJNYW5pZmVzdC5keW5hbWljUm91dGVzW25vcm1hbGl6ZWRTcmNQYWdlXTtcbiAgICBjb25zdCBpc1ByZXJlbmRlcmVkID0gcHJlcmVuZGVyTWFuaWZlc3Qucm91dGVzW3Jlc29sdmVkUGF0aG5hbWVdO1xuICAgIGxldCBpc1NTRyA9IEJvb2xlYW4ocHJlcmVuZGVySW5mbyB8fCBpc1ByZXJlbmRlcmVkIHx8IHByZXJlbmRlck1hbmlmZXN0LnJvdXRlc1tub3JtYWxpemVkU3JjUGFnZV0pO1xuICAgIGNvbnN0IHVzZXJBZ2VudCA9IHJlcS5oZWFkZXJzWyd1c2VyLWFnZW50J10gfHwgJyc7XG4gICAgY29uc3QgYm90VHlwZSA9IGdldEJvdFR5cGUodXNlckFnZW50KTtcbiAgICBjb25zdCBpc0h0bWxCb3QgPSBpc0h0bWxCb3RSZXF1ZXN0KHJlcSk7XG4gICAgLyoqXG4gICAqIElmIHRydWUsIHRoaXMgaW5kaWNhdGVzIHRoYXQgdGhlIHJlcXVlc3QgYmVpbmcgbWFkZSBpcyBmb3IgYW4gYXBwXG4gICAqIHByZWZldGNoIHJlcXVlc3QuXG4gICAqLyBjb25zdCBpc1ByZWZldGNoUlNDUmVxdWVzdCA9IGdldFJlcXVlc3RNZXRhKHJlcSwgJ2lzUHJlZmV0Y2hSU0NSZXF1ZXN0JykgPz8gQm9vbGVhbihyZXEuaGVhZGVyc1tORVhUX1JPVVRFUl9QUkVGRVRDSF9IRUFERVJdKTtcbiAgICAvLyBOT1RFOiBEb24ndCBkZWxldGUgaGVhZGVyc1tSU0NdIHlldCwgaXQgc3RpbGwgbmVlZHMgdG8gYmUgdXNlZCBpbiByZW5kZXJUb0hUTUwgbGF0ZXJcbiAgICBjb25zdCBpc1JTQ1JlcXVlc3QgPSBnZXRSZXF1ZXN0TWV0YShyZXEsICdpc1JTQ1JlcXVlc3QnKSA/PyBCb29sZWFuKHJlcS5oZWFkZXJzW1JTQ19IRUFERVJdKTtcbiAgICBjb25zdCBpc1Bvc3NpYmxlU2VydmVyQWN0aW9uID0gZ2V0SXNQb3NzaWJsZVNlcnZlckFjdGlvbihyZXEpO1xuICAgIC8qKlxuICAgKiBJZiB0aGUgcm91dGUgYmVpbmcgcmVuZGVyZWQgaXMgYW4gYXBwIHBhZ2UsIGFuZCB0aGUgcHByIGZlYXR1cmUgaGFzIGJlZW5cbiAgICogZW5hYmxlZCwgdGhlbiB0aGUgZ2l2ZW4gcm91dGUgX2NvdWxkXyBzdXBwb3J0IFBQUi5cbiAgICovIGNvbnN0IGNvdWxkU3VwcG9ydFBQUiA9IGNoZWNrSXNBcHBQUFJFbmFibGVkKG5leHRDb25maWcuZXhwZXJpbWVudGFsLnBwcik7XG4gICAgLy8gV2hlbiBlbmFibGVkLCB0aGlzIHdpbGwgYWxsb3cgdGhlIHVzZSBvZiB0aGUgYD9fX25leHRwcHJvbmx5YCBxdWVyeSB0b1xuICAgIC8vIGVuYWJsZSBkZWJ1Z2dpbmcgb2YgdGhlIHN0YXRpYyBzaGVsbC5cbiAgICBjb25zdCBoYXNEZWJ1Z1N0YXRpY1NoZWxsUXVlcnkgPSBwcm9jZXNzLmVudi5fX05FWFRfRVhQRVJJTUVOVEFMX1NUQVRJQ19TSEVMTF9ERUJVR0dJTkcgPT09ICcxJyAmJiB0eXBlb2YgcXVlcnkuX19uZXh0cHByb25seSAhPT0gJ3VuZGVmaW5lZCcgJiYgY291bGRTdXBwb3J0UFBSO1xuICAgIC8vIFdoZW4gZW5hYmxlZCwgdGhpcyB3aWxsIGFsbG93IHRoZSB1c2Ugb2YgdGhlIGA/X19uZXh0cHByb25seWAgcXVlcnlcbiAgICAvLyB0byBlbmFibGUgZGVidWdnaW5nIG9mIHRoZSBmYWxsYmFjayBzaGVsbC5cbiAgICBjb25zdCBoYXNEZWJ1Z0ZhbGxiYWNrU2hlbGxRdWVyeSA9IGhhc0RlYnVnU3RhdGljU2hlbGxRdWVyeSAmJiBxdWVyeS5fX25leHRwcHJvbmx5ID09PSAnZmFsbGJhY2snO1xuICAgIC8vIFRoaXMgcGFnZSBzdXBwb3J0cyBQUFIgaWYgaXQgaXMgbWFya2VkIGFzIGJlaW5nIGBQQVJUSUFMTFlfU1RBVElDYCBpbiB0aGVcbiAgICAvLyBwcmVyZW5kZXIgbWFuaWZlc3QgYW5kIHRoaXMgaXMgYW4gYXBwIHBhZ2UuXG4gICAgY29uc3QgaXNSb3V0ZVBQUkVuYWJsZWQgPSBjb3VsZFN1cHBvcnRQUFIgJiYgKCgoX3RoaXMgPSBwcmVyZW5kZXJNYW5pZmVzdC5yb3V0ZXNbbm9ybWFsaXplZFNyY1BhZ2VdID8/IHByZXJlbmRlck1hbmlmZXN0LmR5bmFtaWNSb3V0ZXNbbm9ybWFsaXplZFNyY1BhZ2VdKSA9PSBudWxsID8gdm9pZCAwIDogX3RoaXMucmVuZGVyaW5nTW9kZSkgPT09ICdQQVJUSUFMTFlfU1RBVElDJyB8fCAvLyBJZGVhbGx5IHdlJ2Qgd2FudCB0byBjaGVjayB0aGUgYXBwQ29uZmlnIHRvIHNlZSBpZiB0aGlzIHBhZ2UgaGFzIFBQUlxuICAgIC8vIGVuYWJsZWQgb3Igbm90LCBidXQgdGhhdCB3b3VsZCByZXF1aXJlIHBsdW1iaW5nIHRoZSBhcHBDb25maWcgdGhyb3VnaFxuICAgIC8vIHRvIHRoZSBzZXJ2ZXIgZHVyaW5nIGRldmVsb3BtZW50LiBXZSBhc3N1bWUgdGhhdCB0aGUgcGFnZSBzdXBwb3J0cyBpdFxuICAgIC8vIGJ1dCBvbmx5IGR1cmluZyBkZXZlbG9wbWVudC5cbiAgICBoYXNEZWJ1Z1N0YXRpY1NoZWxsUXVlcnkgJiYgKHJvdXRlTW9kdWxlLmlzRGV2ID09PSB0cnVlIHx8IChyb3V0ZXJTZXJ2ZXJDb250ZXh0ID09IG51bGwgPyB2b2lkIDAgOiByb3V0ZXJTZXJ2ZXJDb250ZXh0LmV4cGVyaW1lbnRhbFRlc3RQcm94eSkgPT09IHRydWUpKTtcbiAgICBjb25zdCBpc0RlYnVnU3RhdGljU2hlbGwgPSBoYXNEZWJ1Z1N0YXRpY1NoZWxsUXVlcnkgJiYgaXNSb3V0ZVBQUkVuYWJsZWQ7XG4gICAgLy8gV2Ugc2hvdWxkIGVuYWJsZSBkZWJ1Z2dpbmcgZHluYW1pYyBhY2Nlc3NlcyB3aGVuIHRoZSBzdGF0aWMgc2hlbGxcbiAgICAvLyBkZWJ1Z2dpbmcgaGFzIGJlZW4gZW5hYmxlZCBhbmQgd2UncmUgYWxzbyBpbiBkZXZlbG9wbWVudCBtb2RlLlxuICAgIGNvbnN0IGlzRGVidWdEeW5hbWljQWNjZXNzZXMgPSBpc0RlYnVnU3RhdGljU2hlbGwgJiYgcm91dGVNb2R1bGUuaXNEZXYgPT09IHRydWU7XG4gICAgY29uc3QgaXNEZWJ1Z0ZhbGxiYWNrU2hlbGwgPSBoYXNEZWJ1Z0ZhbGxiYWNrU2hlbGxRdWVyeSAmJiBpc1JvdXRlUFBSRW5hYmxlZDtcbiAgICAvLyBJZiB3ZSdyZSBpbiBtaW5pbWFsIG1vZGUsIHRoZW4gdHJ5IHRvIGdldCB0aGUgcG9zdHBvbmVkIGluZm9ybWF0aW9uIGZyb21cbiAgICAvLyB0aGUgcmVxdWVzdCBtZXRhZGF0YS4gSWYgYXZhaWxhYmxlLCB1c2UgaXQgZm9yIHJlc3VtaW5nIHRoZSBwb3N0cG9uZWRcbiAgICAvLyByZW5kZXIuXG4gICAgY29uc3QgbWluaW1hbFBvc3Rwb25lZCA9IGlzUm91dGVQUFJFbmFibGVkID8gaW5pdGlhbFBvc3Rwb25lZCA6IHVuZGVmaW5lZDtcbiAgICAvLyBJZiBQUFIgaXMgZW5hYmxlZCwgYW5kIHRoaXMgaXMgYSBSU0MgcmVxdWVzdCAoYnV0IG5vdCBhIHByZWZldGNoKSwgdGhlblxuICAgIC8vIHdlIGNhbiB1c2UgdGhpcyBmYWN0IHRvIG9ubHkgZ2VuZXJhdGUgdGhlIGZsaWdodCBkYXRhIGZvciB0aGUgcmVxdWVzdFxuICAgIC8vIGJlY2F1c2Ugd2UgY2FuJ3QgY2FjaGUgdGhlIEhUTUwgKGFzIGl0J3MgYWxzbyBkeW5hbWljKS5cbiAgICBjb25zdCBpc0R5bmFtaWNSU0NSZXF1ZXN0ID0gaXNSb3V0ZVBQUkVuYWJsZWQgJiYgaXNSU0NSZXF1ZXN0ICYmICFpc1ByZWZldGNoUlNDUmVxdWVzdDtcbiAgICAvLyBOZWVkIHRvIHJlYWQgdGhpcyBiZWZvcmUgaXQncyBzdHJpcHBlZCBieSBzdHJpcEZsaWdodEhlYWRlcnMuIFdlIGRvbid0XG4gICAgLy8gbmVlZCB0byB0cmFuc2ZlciBpdCB0byB0aGUgcmVxdWVzdCBtZXRhIGJlY2F1c2UgaXQncyBvbmx5IHJlYWRcbiAgICAvLyB3aXRoaW4gdGhpcyBmdW5jdGlvbjsgdGhlIHN0YXRpYyBzZWdtZW50IGRhdGEgc2hvdWxkIGhhdmUgYWxyZWFkeSBiZWVuXG4gICAgLy8gZ2VuZXJhdGVkLCBzbyB3ZSB3aWxsIGFsd2F5cyBlaXRoZXIgcmV0dXJuIGEgc3RhdGljIHJlc3BvbnNlIG9yIGEgNDA0LlxuICAgIGNvbnN0IHNlZ21lbnRQcmVmZXRjaEhlYWRlciA9IGdldFJlcXVlc3RNZXRhKHJlcSwgJ3NlZ21lbnRQcmVmZXRjaFJTQ1JlcXVlc3QnKTtcbiAgICAvLyBUT0RPOiBpbnZlc3RpZ2F0ZSBleGlzdGluZyBidWcgd2l0aCBzaG91bGRTZXJ2ZVN0cmVhbWluZ01ldGFkYXRhIGFsd2F5c1xuICAgIC8vIGJlaW5nIHRydWUgZm9yIGEgcmV2YWxpZGF0ZSBkdWUgdG8gbW9kaWZ5aW5nIHRoZSBiYXNlLXNlcnZlciB0aGlzLnJlbmRlck9wdHNcbiAgICAvLyB3aGVuIGZpeGluZyB0aGlzIHRvIGNvcnJlY3QgbG9naWMgaXQgY2F1c2VzIGh5ZHJhdGlvbiBpc3N1ZSBzaW5jZSB3ZSBzZXRcbiAgICAvLyBzZXJ2ZVN0cmVhbWluZ01ldGFkYXRhIHRvIHRydWUgZHVyaW5nIGV4cG9ydFxuICAgIGxldCBzZXJ2ZVN0cmVhbWluZ01ldGFkYXRhID0gIXVzZXJBZ2VudCA/IHRydWUgOiBzaG91bGRTZXJ2ZVN0cmVhbWluZ01ldGFkYXRhKHVzZXJBZ2VudCwgbmV4dENvbmZpZy5odG1sTGltaXRlZEJvdHMpO1xuICAgIGlmIChpc0h0bWxCb3QgJiYgaXNSb3V0ZVBQUkVuYWJsZWQpIHtcbiAgICAgICAgaXNTU0cgPSBmYWxzZTtcbiAgICAgICAgc2VydmVTdHJlYW1pbmdNZXRhZGF0YSA9IGZhbHNlO1xuICAgIH1cbiAgICAvLyBJbiBkZXZlbG9wbWVudCwgd2UgYWx3YXlzIHdhbnQgdG8gZ2VuZXJhdGUgZHluYW1pYyBIVE1MLlxuICAgIGxldCBzdXBwb3J0c0R5bmFtaWNSZXNwb25zZSA9IC8vIElmIHdlJ3JlIGluIGRldmVsb3BtZW50LCB3ZSBhbHdheXMgc3VwcG9ydCBkeW5hbWljIEhUTUwsIHVubGVzcyBpdCdzXG4gICAgLy8gYSBkYXRhIHJlcXVlc3QsIGluIHdoaWNoIGNhc2Ugd2Ugb25seSBwcm9kdWNlIHN0YXRpYyBIVE1MLlxuICAgIHJvdXRlTW9kdWxlLmlzRGV2ID09PSB0cnVlIHx8IC8vIElmIHRoaXMgaXMgbm90IFNTRyBvciBkb2VzIG5vdCBoYXZlIHN0YXRpYyBwYXRocywgdGhlbiBpdCBzdXBwb3J0c1xuICAgIC8vIGR5bmFtaWMgSFRNTC5cbiAgICAhaXNTU0cgfHwgLy8gSWYgdGhpcyByZXF1ZXN0IGhhcyBwcm92aWRlZCBwb3N0cG9uZWQgZGF0YSwgaXQgc3VwcG9ydHMgZHluYW1pY1xuICAgIC8vIEhUTUwuXG4gICAgdHlwZW9mIGluaXRpYWxQb3N0cG9uZWQgPT09ICdzdHJpbmcnIHx8IC8vIElmIHRoaXMgaXMgYSBkeW5hbWljIFJTQyByZXF1ZXN0LCB0aGVuIHRoaXMgcmVuZGVyIHN1cHBvcnRzIGR5bmFtaWNcbiAgICAvLyBIVE1MIChpdCdzIGR5bmFtaWMpLlxuICAgIGlzRHluYW1pY1JTQ1JlcXVlc3Q7XG4gICAgLy8gV2hlbiBodG1sIGJvdHMgcmVxdWVzdCBQUFIgcGFnZSwgcGVyZm9ybSB0aGUgZnVsbCBkeW5hbWljIHJlbmRlcmluZy5cbiAgICBjb25zdCBzaG91bGRXYWl0T25BbGxSZWFkeSA9IGlzSHRtbEJvdCAmJiBpc1JvdXRlUFBSRW5hYmxlZDtcbiAgICBsZXQgc3NnQ2FjaGVLZXkgPSBudWxsO1xuICAgIGlmICghaXNEcmFmdE1vZGUgJiYgaXNTU0cgJiYgIXN1cHBvcnRzRHluYW1pY1Jlc3BvbnNlICYmICFpc1Bvc3NpYmxlU2VydmVyQWN0aW9uICYmICFtaW5pbWFsUG9zdHBvbmVkICYmICFpc0R5bmFtaWNSU0NSZXF1ZXN0KSB7XG4gICAgICAgIHNzZ0NhY2hlS2V5ID0gcmVzb2x2ZWRQYXRobmFtZTtcbiAgICB9XG4gICAgLy8gdGhlIHN0YXRpY1BhdGhLZXkgZGlmZmVycyBmcm9tIHNzZ0NhY2hlS2V5IHNpbmNlXG4gICAgLy8gc3NnQ2FjaGVLZXkgaXMgbnVsbCBpbiBkZXYgc2luY2Ugd2UncmUgYWx3YXlzIGluIFwiZHluYW1pY1wiXG4gICAgLy8gbW9kZSBpbiBkZXYgdG8gYnlwYXNzIHRoZSBjYWNoZSwgYnV0IHdlIHN0aWxsIG5lZWQgdG8gaG9ub3JcbiAgICAvLyBkeW5hbWljUGFyYW1zID0gZmFsc2UgaW4gZGV2IG1vZGVcbiAgICBsZXQgc3RhdGljUGF0aEtleSA9IHNzZ0NhY2hlS2V5O1xuICAgIGlmICghc3RhdGljUGF0aEtleSAmJiByb3V0ZU1vZHVsZS5pc0Rldikge1xuICAgICAgICBzdGF0aWNQYXRoS2V5ID0gcmVzb2x2ZWRQYXRobmFtZTtcbiAgICB9XG4gICAgY29uc3QgQ29tcG9uZW50TW9kID0ge1xuICAgICAgICAuLi5lbnRyeUJhc2UsXG4gICAgICAgIHRyZWUsXG4gICAgICAgIHBhZ2VzLFxuICAgICAgICBHbG9iYWxFcnJvcixcbiAgICAgICAgaGFuZGxlcixcbiAgICAgICAgcm91dGVNb2R1bGUsXG4gICAgICAgIF9fbmV4dF9hcHBfX1xuICAgIH07XG4gICAgLy8gQmVmb3JlIHJlbmRlcmluZyAod2hpY2ggaW5pdGlhbGl6ZXMgY29tcG9uZW50IHRyZWUgbW9kdWxlcyksIHdlIGhhdmUgdG9cbiAgICAvLyBzZXQgdGhlIHJlZmVyZW5jZSBtYW5pZmVzdHMgdG8gb3VyIGdsb2JhbCBzdG9yZSBzbyBTZXJ2ZXIgQWN0aW9uJ3NcbiAgICAvLyBlbmNyeXB0aW9uIHV0aWwgY2FuIGFjY2VzcyB0byB0aGVtIGF0IHRoZSB0b3AgbGV2ZWwgb2YgdGhlIHBhZ2UgbW9kdWxlLlxuICAgIGlmIChzZXJ2ZXJBY3Rpb25zTWFuaWZlc3QgJiYgY2xpZW50UmVmZXJlbmNlTWFuaWZlc3QpIHtcbiAgICAgICAgc2V0UmVmZXJlbmNlTWFuaWZlc3RzU2luZ2xldG9uKHtcbiAgICAgICAgICAgIHBhZ2U6IHNyY1BhZ2UsXG4gICAgICAgICAgICBjbGllbnRSZWZlcmVuY2VNYW5pZmVzdCxcbiAgICAgICAgICAgIHNlcnZlckFjdGlvbnNNYW5pZmVzdCxcbiAgICAgICAgICAgIHNlcnZlck1vZHVsZU1hcDogY3JlYXRlU2VydmVyTW9kdWxlTWFwKHtcbiAgICAgICAgICAgICAgICBzZXJ2ZXJBY3Rpb25zTWFuaWZlc3RcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBjb25zdCBtZXRob2QgPSByZXEubWV0aG9kIHx8ICdHRVQnO1xuICAgIGNvbnN0IHRyYWNlciA9IGdldFRyYWNlcigpO1xuICAgIGNvbnN0IGFjdGl2ZVNwYW4gPSB0cmFjZXIuZ2V0QWN0aXZlU2NvcGVTcGFuKCk7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3QgaW52b2tlUm91dGVNb2R1bGUgPSBhc3luYyAoc3BhbiwgY29udGV4dCk9PntcbiAgICAgICAgICAgIGNvbnN0IG5leHRSZXEgPSBuZXcgTm9kZU5leHRSZXF1ZXN0KHJlcSk7XG4gICAgICAgICAgICBjb25zdCBuZXh0UmVzID0gbmV3IE5vZGVOZXh0UmVzcG9uc2UocmVzKTtcbiAgICAgICAgICAgIC8vIFRPRE86IGFkYXB0IGZvciBwdXR0aW5nIHRoZSBSREMgaW5zaWRlIHRoZSBwb3N0cG9uZWQgZGF0YVxuICAgICAgICAgICAgLy8gSWYgd2UncmUgaW4gZGV2LCBhbmQgdGhpcyBpc24ndCBhIHByZWZldGNoIG9yIGEgc2VydmVyIGFjdGlvbixcbiAgICAgICAgICAgIC8vIHdlIHNob3VsZCBzZWVkIHRoZSByZXN1bWUgZGF0YSBjYWNoZS5cbiAgICAgICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xuICAgICAgICAgICAgICAgIGlmIChuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5keW5hbWljSU8gJiYgIWlzUHJlZmV0Y2hSU0NSZXF1ZXN0ICYmICFjb250ZXh0LnJlbmRlck9wdHMuaXNQb3NzaWJsZVNlcnZlckFjdGlvbikge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB3YXJtdXAgPSBhd2FpdCByb3V0ZU1vZHVsZS53YXJtdXAobmV4dFJlcSwgbmV4dFJlcywgY29udGV4dCk7XG4gICAgICAgICAgICAgICAgICAgIC8vIElmIHRoZSB3YXJtdXAgaXMgc3VjY2Vzc2Z1bCwgd2Ugc2hvdWxkIHVzZSB0aGUgcmVzdW1lIGRhdGFcbiAgICAgICAgICAgICAgICAgICAgLy8gY2FjaGUgZnJvbSB0aGUgd2FybXVwLlxuICAgICAgICAgICAgICAgICAgICBpZiAod2FybXVwLm1ldGFkYXRhLnJlbmRlclJlc3VtZURhdGFDYWNoZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dC5yZW5kZXJPcHRzLnJlbmRlclJlc3VtZURhdGFDYWNoZSA9IHdhcm11cC5tZXRhZGF0YS5yZW5kZXJSZXN1bWVEYXRhQ2FjaGU7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gcm91dGVNb2R1bGUucmVuZGVyKG5leHRSZXEsIG5leHRSZXMsIGNvbnRleHQpLmZpbmFsbHkoKCk9PntcbiAgICAgICAgICAgICAgICBpZiAoIXNwYW4pIHJldHVybjtcbiAgICAgICAgICAgICAgICBzcGFuLnNldEF0dHJpYnV0ZXMoe1xuICAgICAgICAgICAgICAgICAgICAnaHR0cC5zdGF0dXNfY29kZSc6IHJlcy5zdGF0dXNDb2RlLFxuICAgICAgICAgICAgICAgICAgICAnbmV4dC5yc2MnOiBmYWxzZVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJvb3RTcGFuQXR0cmlidXRlcyA9IHRyYWNlci5nZXRSb290U3BhbkF0dHJpYnV0ZXMoKTtcbiAgICAgICAgICAgICAgICAvLyBXZSB3ZXJlIHVuYWJsZSB0byBnZXQgYXR0cmlidXRlcywgcHJvYmFibHkgT1RFTCBpcyBub3QgZW5hYmxlZFxuICAgICAgICAgICAgICAgIGlmICghcm9vdFNwYW5BdHRyaWJ1dGVzKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHJvb3RTcGFuQXR0cmlidXRlcy5nZXQoJ25leHQuc3Bhbl90eXBlJykgIT09IEJhc2VTZXJ2ZXJTcGFuLmhhbmRsZVJlcXVlc3QpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGBVbmV4cGVjdGVkIHJvb3Qgc3BhbiB0eXBlICcke3Jvb3RTcGFuQXR0cmlidXRlcy5nZXQoJ25leHQuc3Bhbl90eXBlJyl9Jy4gUGxlYXNlIHJlcG9ydCB0aGlzIE5leHQuanMgaXNzdWUgaHR0cHM6Ly9naXRodWIuY29tL3ZlcmNlbC9uZXh0LmpzYCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3Qgcm91dGUgPSByb290U3BhbkF0dHJpYnV0ZXMuZ2V0KCduZXh0LnJvdXRlJyk7XG4gICAgICAgICAgICAgICAgaWYgKHJvdXRlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5hbWUgPSBgJHttZXRob2R9ICR7cm91dGV9YDtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi5zZXRBdHRyaWJ1dGVzKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICduZXh0LnJvdXRlJzogcm91dGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAnaHR0cC5yb3V0ZSc6IHJvdXRlLFxuICAgICAgICAgICAgICAgICAgICAgICAgJ25leHQuc3Bhbl9uYW1lJzogbmFtZVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi51cGRhdGVOYW1lKG5hbWUpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4udXBkYXRlTmFtZShgJHttZXRob2R9ICR7cmVxLnVybH1gKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgZG9SZW5kZXIgPSBhc3luYyAoeyBzcGFuLCBwb3N0cG9uZWQsIGZhbGxiYWNrUm91dGVQYXJhbXMgfSk9PntcbiAgICAgICAgICAgIGNvbnN0IGNvbnRleHQgPSB7XG4gICAgICAgICAgICAgICAgcXVlcnksXG4gICAgICAgICAgICAgICAgcGFyYW1zLFxuICAgICAgICAgICAgICAgIHBhZ2U6IG5vcm1hbGl6ZWRTcmNQYWdlLFxuICAgICAgICAgICAgICAgIHNoYXJlZENvbnRleHQ6IHtcbiAgICAgICAgICAgICAgICAgICAgYnVpbGRJZFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgc2VydmVyQ29tcG9uZW50c0htckNhY2hlOiBnZXRSZXF1ZXN0TWV0YShyZXEsICdzZXJ2ZXJDb21wb25lbnRzSG1yQ2FjaGUnKSxcbiAgICAgICAgICAgICAgICBmYWxsYmFja1JvdXRlUGFyYW1zLFxuICAgICAgICAgICAgICAgIHJlbmRlck9wdHM6IHtcbiAgICAgICAgICAgICAgICAgICAgQXBwOiAoKT0+bnVsbCxcbiAgICAgICAgICAgICAgICAgICAgRG9jdW1lbnQ6ICgpPT5udWxsLFxuICAgICAgICAgICAgICAgICAgICBwYWdlQ29uZmlnOiB7fSxcbiAgICAgICAgICAgICAgICAgICAgQ29tcG9uZW50TW9kLFxuICAgICAgICAgICAgICAgICAgICBDb21wb25lbnQ6IGludGVyb3BEZWZhdWx0KENvbXBvbmVudE1vZCksXG4gICAgICAgICAgICAgICAgICAgIHBhcmFtcyxcbiAgICAgICAgICAgICAgICAgICAgcm91dGVNb2R1bGUsXG4gICAgICAgICAgICAgICAgICAgIHBhZ2U6IHNyY1BhZ2UsXG4gICAgICAgICAgICAgICAgICAgIHBvc3Rwb25lZCxcbiAgICAgICAgICAgICAgICAgICAgc2hvdWxkV2FpdE9uQWxsUmVhZHksXG4gICAgICAgICAgICAgICAgICAgIHNlcnZlU3RyZWFtaW5nTWV0YWRhdGEsXG4gICAgICAgICAgICAgICAgICAgIHN1cHBvcnRzRHluYW1pY1Jlc3BvbnNlOiB0eXBlb2YgcG9zdHBvbmVkID09PSAnc3RyaW5nJyB8fCBzdXBwb3J0c0R5bmFtaWNSZXNwb25zZSxcbiAgICAgICAgICAgICAgICAgICAgYnVpbGRNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICAgICAgbmV4dEZvbnRNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICAgICAgcmVhY3RMb2FkYWJsZU1hbmlmZXN0LFxuICAgICAgICAgICAgICAgICAgICBzdWJyZXNvdXJjZUludGVncml0eU1hbmlmZXN0LFxuICAgICAgICAgICAgICAgICAgICBzZXJ2ZXJBY3Rpb25zTWFuaWZlc3QsXG4gICAgICAgICAgICAgICAgICAgIGNsaWVudFJlZmVyZW5jZU1hbmlmZXN0LFxuICAgICAgICAgICAgICAgICAgICBzZXRJc3JTdGF0dXM6IHJvdXRlclNlcnZlckNvbnRleHQgPT0gbnVsbCA/IHZvaWQgMCA6IHJvdXRlclNlcnZlckNvbnRleHQuc2V0SXNyU3RhdHVzLFxuICAgICAgICAgICAgICAgICAgICBkaXI6IHJvdXRlTW9kdWxlLnByb2plY3REaXIsXG4gICAgICAgICAgICAgICAgICAgIGlzRHJhZnRNb2RlLFxuICAgICAgICAgICAgICAgICAgICBpc1JldmFsaWRhdGU6IGlzU1NHICYmICFwb3N0cG9uZWQgJiYgIWlzRHluYW1pY1JTQ1JlcXVlc3QsXG4gICAgICAgICAgICAgICAgICAgIGJvdFR5cGUsXG4gICAgICAgICAgICAgICAgICAgIGlzT25EZW1hbmRSZXZhbGlkYXRlLFxuICAgICAgICAgICAgICAgICAgICBpc1Bvc3NpYmxlU2VydmVyQWN0aW9uLFxuICAgICAgICAgICAgICAgICAgICBhc3NldFByZWZpeDogbmV4dENvbmZpZy5hc3NldFByZWZpeCxcbiAgICAgICAgICAgICAgICAgICAgbmV4dENvbmZpZ091dHB1dDogbmV4dENvbmZpZy5vdXRwdXQsXG4gICAgICAgICAgICAgICAgICAgIGNyb3NzT3JpZ2luOiBuZXh0Q29uZmlnLmNyb3NzT3JpZ2luLFxuICAgICAgICAgICAgICAgICAgICB0cmFpbGluZ1NsYXNoOiBuZXh0Q29uZmlnLnRyYWlsaW5nU2xhc2gsXG4gICAgICAgICAgICAgICAgICAgIHByZXZpZXdQcm9wczogcHJlcmVuZGVyTWFuaWZlc3QucHJldmlldyxcbiAgICAgICAgICAgICAgICAgICAgZGVwbG95bWVudElkOiBuZXh0Q29uZmlnLmRlcGxveW1lbnRJZCxcbiAgICAgICAgICAgICAgICAgICAgZW5hYmxlVGFpbnRpbmc6IG5leHRDb25maWcuZXhwZXJpbWVudGFsLnRhaW50LFxuICAgICAgICAgICAgICAgICAgICBodG1sTGltaXRlZEJvdHM6IG5leHRDb25maWcuaHRtbExpbWl0ZWRCb3RzLFxuICAgICAgICAgICAgICAgICAgICBkZXZ0b29sU2VnbWVudEV4cGxvcmVyOiBuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5kZXZ0b29sU2VnbWVudEV4cGxvcmVyLFxuICAgICAgICAgICAgICAgICAgICByZWFjdE1heEhlYWRlcnNMZW5ndGg6IG5leHRDb25maWcucmVhY3RNYXhIZWFkZXJzTGVuZ3RoLFxuICAgICAgICAgICAgICAgICAgICBtdWx0aVpvbmVEcmFmdE1vZGUsXG4gICAgICAgICAgICAgICAgICAgIGluY3JlbWVudGFsQ2FjaGU6IGdldFJlcXVlc3RNZXRhKHJlcSwgJ2luY3JlbWVudGFsQ2FjaGUnKSxcbiAgICAgICAgICAgICAgICAgICAgY2FjaGVMaWZlUHJvZmlsZXM6IG5leHRDb25maWcuZXhwZXJpbWVudGFsLmNhY2hlTGlmZSxcbiAgICAgICAgICAgICAgICAgICAgYmFzZVBhdGg6IG5leHRDb25maWcuYmFzZVBhdGgsXG4gICAgICAgICAgICAgICAgICAgIHNlcnZlckFjdGlvbnM6IG5leHRDb25maWcuZXhwZXJpbWVudGFsLnNlcnZlckFjdGlvbnMsXG4gICAgICAgICAgICAgICAgICAgIC4uLmlzRGVidWdTdGF0aWNTaGVsbCB8fCBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzID8ge1xuICAgICAgICAgICAgICAgICAgICAgICAgbmV4dEV4cG9ydDogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHN1cHBvcnRzRHluYW1pY1Jlc3BvbnNlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzU3RhdGljR2VuZXJhdGlvbjogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzUmV2YWxpZGF0ZTogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzRGVidWdEeW5hbWljQWNjZXNzZXM6IGlzRGVidWdEeW5hbWljQWNjZXNzZXNcbiAgICAgICAgICAgICAgICAgICAgfSA6IHt9LFxuICAgICAgICAgICAgICAgICAgICBleHBlcmltZW50YWw6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzUm91dGVQUFJFbmFibGVkLFxuICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlVGltZTogbmV4dENvbmZpZy5leHBpcmVUaW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhbGVUaW1lczogbmV4dENvbmZpZy5leHBlcmltZW50YWwuc3RhbGVUaW1lcyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGR5bmFtaWNJTzogQm9vbGVhbihuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5keW5hbWljSU8pLFxuICAgICAgICAgICAgICAgICAgICAgICAgY2xpZW50U2VnbWVudENhY2hlOiBCb29sZWFuKG5leHRDb25maWcuZXhwZXJpbWVudGFsLmNsaWVudFNlZ21lbnRDYWNoZSksXG4gICAgICAgICAgICAgICAgICAgICAgICBkeW5hbWljT25Ib3ZlcjogQm9vbGVhbihuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5keW5hbWljT25Ib3ZlciksXG4gICAgICAgICAgICAgICAgICAgICAgICBpbmxpbmVDc3M6IEJvb2xlYW4obmV4dENvbmZpZy5leHBlcmltZW50YWwuaW5saW5lQ3NzKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGF1dGhJbnRlcnJ1cHRzOiBCb29sZWFuKG5leHRDb25maWcuZXhwZXJpbWVudGFsLmF1dGhJbnRlcnJ1cHRzKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsaWVudFRyYWNlTWV0YWRhdGE6IG5leHRDb25maWcuZXhwZXJpbWVudGFsLmNsaWVudFRyYWNlTWV0YWRhdGEgfHwgW11cbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgd2FpdFVudGlsOiBjdHgud2FpdFVudGlsLFxuICAgICAgICAgICAgICAgICAgICBvbkNsb3NlOiAoY2IpPT57XG4gICAgICAgICAgICAgICAgICAgICAgICByZXMub24oJ2Nsb3NlJywgY2IpO1xuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICBvbkFmdGVyVGFza0Vycm9yOiAoKT0+e30sXG4gICAgICAgICAgICAgICAgICAgIG9uSW5zdHJ1bWVudGF0aW9uUmVxdWVzdEVycm9yOiAoZXJyb3IsIF9yZXF1ZXN0LCBlcnJvckNvbnRleHQpPT5yb3V0ZU1vZHVsZS5vblJlcXVlc3RFcnJvcihyZXEsIGVycm9yLCBlcnJvckNvbnRleHQsIHJvdXRlclNlcnZlckNvbnRleHQpLFxuICAgICAgICAgICAgICAgICAgICBlcnI6IGdldFJlcXVlc3RNZXRhKHJlcSwgJ2ludm9rZUVycm9yJyksXG4gICAgICAgICAgICAgICAgICAgIGRldjogcm91dGVNb2R1bGUuaXNEZXZcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgaW52b2tlUm91dGVNb2R1bGUoc3BhbiwgY29udGV4dCk7XG4gICAgICAgICAgICBjb25zdCB7IG1ldGFkYXRhIH0gPSByZXN1bHQ7XG4gICAgICAgICAgICBjb25zdCB7IGNhY2hlQ29udHJvbCwgaGVhZGVycyA9IHt9LCAvLyBBZGQgYW55IGZldGNoIHRhZ3MgdGhhdCB3ZXJlIG9uIHRoZSBwYWdlIHRvIHRoZSByZXNwb25zZSBoZWFkZXJzLlxuICAgICAgICAgICAgZmV0Y2hUYWdzOiBjYWNoZVRhZ3MgfSA9IG1ldGFkYXRhO1xuICAgICAgICAgICAgaWYgKGNhY2hlVGFncykge1xuICAgICAgICAgICAgICAgIGhlYWRlcnNbTkVYVF9DQUNIRV9UQUdTX0hFQURFUl0gPSBjYWNoZVRhZ3M7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBQdWxsIGFueSBmZXRjaCBtZXRyaWNzIGZyb20gdGhlIHJlbmRlciBvbnRvIHRoZSByZXF1ZXN0LlxuICAgICAgICAgICAgO1xuICAgICAgICAgICAgcmVxLmZldGNoTWV0cmljcyA9IG1ldGFkYXRhLmZldGNoTWV0cmljcztcbiAgICAgICAgICAgIC8vIHdlIGRvbid0IHRocm93IHN0YXRpYyB0byBkeW5hbWljIGVycm9ycyBpbiBkZXYgYXMgaXNTU0dcbiAgICAgICAgICAgIC8vIGlzIGEgYmVzdCBndWVzcyBpbiBkZXYgc2luY2Ugd2UgZG9uJ3QgaGF2ZSB0aGUgcHJlcmVuZGVyIHBhc3NcbiAgICAgICAgICAgIC8vIHRvIGtub3cgd2hldGhlciB0aGUgcGF0aCBpcyBhY3R1YWxseSBzdGF0aWMgb3Igbm90XG4gICAgICAgICAgICBpZiAoaXNTU0cgJiYgKGNhY2hlQ29udHJvbCA9PSBudWxsID8gdm9pZCAwIDogY2FjaGVDb250cm9sLnJldmFsaWRhdGUpID09PSAwICYmICFyb3V0ZU1vZHVsZS5pc0RldiAmJiAhaXNSb3V0ZVBQUkVuYWJsZWQpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBzdGF0aWNCYWlsb3V0SW5mbyA9IG1ldGFkYXRhLnN0YXRpY0JhaWxvdXRJbmZvO1xuICAgICAgICAgICAgICAgIGNvbnN0IGVyciA9IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgRXJyb3IoYFBhZ2UgY2hhbmdlZCBmcm9tIHN0YXRpYyB0byBkeW5hbWljIGF0IHJ1bnRpbWUgJHtyZXNvbHZlZFBhdGhuYW1lfSR7KHN0YXRpY0JhaWxvdXRJbmZvID09IG51bGwgPyB2b2lkIDAgOiBzdGF0aWNCYWlsb3V0SW5mby5kZXNjcmlwdGlvbikgPyBgLCByZWFzb246ICR7c3RhdGljQmFpbG91dEluZm8uZGVzY3JpcHRpb259YCA6IGBgfWAgKyBgXFxuc2VlIG1vcmUgaGVyZSBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9hcHAtc3RhdGljLXRvLWR5bmFtaWMtZXJyb3JgKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkUxMzJcIixcbiAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGlmIChzdGF0aWNCYWlsb3V0SW5mbyA9PSBudWxsID8gdm9pZCAwIDogc3RhdGljQmFpbG91dEluZm8uc3RhY2spIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RhY2sgPSBzdGF0aWNCYWlsb3V0SW5mby5zdGFjaztcbiAgICAgICAgICAgICAgICAgICAgZXJyLnN0YWNrID0gZXJyLm1lc3NhZ2UgKyBzdGFjay5zdWJzdHJpbmcoc3RhY2suaW5kZXhPZignXFxuJykpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHZhbHVlOiB7XG4gICAgICAgICAgICAgICAgICAgIGtpbmQ6IENhY2hlZFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgICAgICAgICAgICAgaHRtbDogcmVzdWx0LFxuICAgICAgICAgICAgICAgICAgICBoZWFkZXJzLFxuICAgICAgICAgICAgICAgICAgICByc2NEYXRhOiBtZXRhZGF0YS5mbGlnaHREYXRhLFxuICAgICAgICAgICAgICAgICAgICBwb3N0cG9uZWQ6IG1ldGFkYXRhLnBvc3Rwb25lZCxcbiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBtZXRhZGF0YS5zdGF0dXNDb2RlLFxuICAgICAgICAgICAgICAgICAgICBzZWdtZW50RGF0YTogbWV0YWRhdGEuc2VnbWVudERhdGFcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgcmVzcG9uc2VHZW5lcmF0b3IgPSBhc3luYyAoeyBoYXNSZXNvbHZlZCwgcHJldmlvdXNDYWNoZUVudHJ5LCBpc1JldmFsaWRhdGluZywgc3BhbiB9KT0+e1xuICAgICAgICAgICAgY29uc3QgaXNQcm9kdWN0aW9uID0gcm91dGVNb2R1bGUuaXNEZXYgPT09IGZhbHNlO1xuICAgICAgICAgICAgY29uc3QgZGlkUmVzcG9uZCA9IGhhc1Jlc29sdmVkIHx8IHJlcy53cml0YWJsZUVuZGVkO1xuICAgICAgICAgICAgLy8gc2tpcCBvbi1kZW1hbmQgcmV2YWxpZGF0ZSBpZiBjYWNoZSBpcyBub3QgcHJlc2VudCBhbmRcbiAgICAgICAgICAgIC8vIHJldmFsaWRhdGUtaWYtZ2VuZXJhdGVkIGlzIHNldFxuICAgICAgICAgICAgaWYgKGlzT25EZW1hbmRSZXZhbGlkYXRlICYmIHJldmFsaWRhdGVPbmx5R2VuZXJhdGVkICYmICFwcmV2aW91c0NhY2hlRW50cnkgJiYgIW1pbmltYWxNb2RlKSB7XG4gICAgICAgICAgICAgICAgaWYgKHJvdXRlclNlcnZlckNvbnRleHQgPT0gbnVsbCA/IHZvaWQgMCA6IHJvdXRlclNlcnZlckNvbnRleHQucmVuZGVyNDA0KSB7XG4gICAgICAgICAgICAgICAgICAgIGF3YWl0IHJvdXRlclNlcnZlckNvbnRleHQucmVuZGVyNDA0KHJlcSwgcmVzKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICByZXMuc3RhdHVzQ29kZSA9IDQwNDtcbiAgICAgICAgICAgICAgICAgICAgcmVzLmVuZCgnVGhpcyBwYWdlIGNvdWxkIG5vdCBiZSBmb3VuZCcpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGxldCBmYWxsYmFja01vZGU7XG4gICAgICAgICAgICBpZiAocHJlcmVuZGVySW5mbykge1xuICAgICAgICAgICAgICAgIGZhbGxiYWNrTW9kZSA9IHBhcnNlRmFsbGJhY2tGaWVsZChwcmVyZW5kZXJJbmZvLmZhbGxiYWNrKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIFdoZW4gc2VydmluZyBhIGJvdCByZXF1ZXN0LCB3ZSB3YW50IHRvIHNlcnZlIGEgYmxvY2tpbmcgcmVuZGVyIGFuZCBub3RcbiAgICAgICAgICAgIC8vIHRoZSBwcmVyZW5kZXJlZCBwYWdlLiBUaGlzIGVuc3VyZXMgdGhhdCB0aGUgY29ycmVjdCBjb250ZW50IGlzIHNlcnZlZFxuICAgICAgICAgICAgLy8gdG8gdGhlIGJvdCBpbiB0aGUgaGVhZC5cbiAgICAgICAgICAgIGlmIChmYWxsYmFja01vZGUgPT09IEZhbGxiYWNrTW9kZS5QUkVSRU5ERVIgJiYgaXNCb3QodXNlckFnZW50KSkge1xuICAgICAgICAgICAgICAgIGZhbGxiYWNrTW9kZSA9IEZhbGxiYWNrTW9kZS5CTE9DS0lOR19TVEFUSUNfUkVOREVSO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKChwcmV2aW91c0NhY2hlRW50cnkgPT0gbnVsbCA/IHZvaWQgMCA6IHByZXZpb3VzQ2FjaGVFbnRyeS5pc1N0YWxlKSA9PT0gLTEpIHtcbiAgICAgICAgICAgICAgICBpc09uRGVtYW5kUmV2YWxpZGF0ZSA9IHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBUT0RPOiBhZGFwdCBmb3IgUFBSXG4gICAgICAgICAgICAvLyBvbmx5IGFsbG93IG9uLWRlbWFuZCByZXZhbGlkYXRlIGZvciBmYWxsYmFjazogdHJ1ZS9ibG9ja2luZ1xuICAgICAgICAgICAgLy8gb3IgZm9yIHByZXJlbmRlcmVkIGZhbGxiYWNrOiBmYWxzZSBwYXRoc1xuICAgICAgICAgICAgaWYgKGlzT25EZW1hbmRSZXZhbGlkYXRlICYmIChmYWxsYmFja01vZGUgIT09IEZhbGxiYWNrTW9kZS5OT1RfRk9VTkQgfHwgcHJldmlvdXNDYWNoZUVudHJ5KSkge1xuICAgICAgICAgICAgICAgIGZhbGxiYWNrTW9kZSA9IEZhbGxiYWNrTW9kZS5CTE9DS0lOR19TVEFUSUNfUkVOREVSO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFtaW5pbWFsTW9kZSAmJiBmYWxsYmFja01vZGUgIT09IEZhbGxiYWNrTW9kZS5CTE9DS0lOR19TVEFUSUNfUkVOREVSICYmIHN0YXRpY1BhdGhLZXkgJiYgIWRpZFJlc3BvbmQgJiYgIWlzRHJhZnRNb2RlICYmIHBhZ2VJc0R5bmFtaWMgJiYgKGlzUHJvZHVjdGlvbiB8fCAhaXNQcmVyZW5kZXJlZCkpIHtcbiAgICAgICAgICAgICAgICAvLyBpZiB0aGUgcGFnZSBoYXMgZHluYW1pY1BhcmFtczogZmFsc2UgYW5kIHRoaXMgcGF0aG5hbWUgd2Fzbid0XG4gICAgICAgICAgICAgICAgLy8gcHJlcmVuZGVyZWQgdHJpZ2dlciB0aGUgbm8gZmFsbGJhY2sgaGFuZGxpbmdcbiAgICAgICAgICAgICAgICBpZiAoLy8gSW4gZGV2ZWxvcG1lbnQsIGZhbGwgdGhyb3VnaCB0byByZW5kZXIgdG8gaGFuZGxlIG1pc3NpbmdcbiAgICAgICAgICAgICAgICAvLyBnZXRTdGF0aWNQYXRocy5cbiAgICAgICAgICAgICAgICAoaXNQcm9kdWN0aW9uIHx8IHByZXJlbmRlckluZm8pICYmIC8vIFdoZW4gZmFsbGJhY2sgaXNuJ3QgcHJlc2VudCwgYWJvcnQgdGhpcyByZW5kZXIgc28gd2UgNDA0XG4gICAgICAgICAgICAgICAgZmFsbGJhY2tNb2RlID09PSBGYWxsYmFja01vZGUuTk9UX0ZPVU5EKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBOb0ZhbGxiYWNrRXJyb3IoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgbGV0IGZhbGxiYWNrUmVzcG9uc2U7XG4gICAgICAgICAgICAgICAgaWYgKGlzUm91dGVQUFJFbmFibGVkICYmICFpc1JTQ1JlcXVlc3QpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gV2UgdXNlIHRoZSByZXNwb25zZSBjYWNoZSBoZXJlIHRvIGhhbmRsZSB0aGUgcmV2YWxpZGF0aW9uIGFuZFxuICAgICAgICAgICAgICAgICAgICAvLyBtYW5hZ2VtZW50IG9mIHRoZSBmYWxsYmFjayBzaGVsbC5cbiAgICAgICAgICAgICAgICAgICAgZmFsbGJhY2tSZXNwb25zZSA9IGF3YWl0IHJvdXRlTW9kdWxlLmhhbmRsZVJlc3BvbnNlKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhY2hlS2V5OiBpc1Byb2R1Y3Rpb24gPyBub3JtYWxpemVkU3JjUGFnZSA6IG51bGwsXG4gICAgICAgICAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgICAgICAgICBuZXh0Q29uZmlnLFxuICAgICAgICAgICAgICAgICAgICAgICAgcm91dGVLaW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc0ZhbGxiYWNrOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgcHJlcmVuZGVyTWFuaWZlc3QsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc1JvdXRlUFBSRW5hYmxlZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlR2VuZXJhdG9yOiBhc3luYyAoKT0+ZG9SZW5kZXIoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcGFuLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBXZSBwYXNzIGB1bmRlZmluZWRgIGFzIHJlbmRlcmluZyBhIGZhbGxiYWNrIGlzbid0IHJlc3VtZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gaGVyZS5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcG9zdHBvbmVkOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZhbGxiYWNrUm91dGVQYXJhbXM6IC8vIElmIHdlJ3JlIGluIHByb2R1Y3Rpb24gb3Igd2UncmUgZGVidWdnaW5nIHRoZSBmYWxsYmFja1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBzaGVsbCB0aGVuIHdlIHNob3VsZCBwb3N0cG9uZSB3aGVuIGR5bmFtaWMgcGFyYW1zIGFyZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBhY2Nlc3NlZC5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNQcm9kdWN0aW9uIHx8IGlzRGVidWdGYWxsYmFja1NoZWxsID8gZ2V0RmFsbGJhY2tSb3V0ZVBhcmFtcyhub3JtYWxpemVkU3JjUGFnZSkgOiBudWxsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgICAgICAgICAgICB3YWl0VW50aWw6IGN0eC53YWl0VW50aWxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIC8vIElmIHRoZSBmYWxsYmFjayByZXNwb25zZSB3YXMgc2V0IHRvIG51bGwsIHRoZW4gd2Ugc2hvdWxkIHJldHVybiBudWxsLlxuICAgICAgICAgICAgICAgICAgICBpZiAoZmFsbGJhY2tSZXNwb25zZSA9PT0gbnVsbCkgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgICAgICAgIC8vIE90aGVyd2lzZSwgaWYgd2UgZGlkIGdldCBhIGZhbGxiYWNrIHJlc3BvbnNlLCB3ZSBzaG91bGQgcmV0dXJuIGl0LlxuICAgICAgICAgICAgICAgICAgICBpZiAoZmFsbGJhY2tSZXNwb25zZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gUmVtb3ZlIHRoZSBjYWNoZSBjb250cm9sIGZyb20gdGhlIHJlc3BvbnNlIHRvIHByZXZlbnQgaXQgZnJvbSBiZWluZ1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gdXNlZCBpbiB0aGUgc3Vycm91bmRpbmcgY2FjaGUuXG4gICAgICAgICAgICAgICAgICAgICAgICBkZWxldGUgZmFsbGJhY2tSZXNwb25zZS5jYWNoZUNvbnRyb2w7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsbGJhY2tSZXNwb25zZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIE9ubHkgcmVxdWVzdHMgdGhhdCBhcmVuJ3QgcmV2YWxpZGF0aW5nIGNhbiBiZSByZXN1bWVkLiBJZiB3ZSBoYXZlIHRoZVxuICAgICAgICAgICAgLy8gbWluaW1hbCBwb3N0cG9uZWQgZGF0YSwgdGhlbiB3ZSBzaG91bGQgcmVzdW1lIHRoZSByZW5kZXIgd2l0aCBpdC5cbiAgICAgICAgICAgIGNvbnN0IHBvc3Rwb25lZCA9ICFpc09uRGVtYW5kUmV2YWxpZGF0ZSAmJiAhaXNSZXZhbGlkYXRpbmcgJiYgbWluaW1hbFBvc3Rwb25lZCA/IG1pbmltYWxQb3N0cG9uZWQgOiB1bmRlZmluZWQ7XG4gICAgICAgICAgICAvLyBXaGVuIHdlJ3JlIGluIG1pbmltYWwgbW9kZSwgaWYgd2UncmUgdHJ5aW5nIHRvIGRlYnVnIHRoZSBzdGF0aWMgc2hlbGwsXG4gICAgICAgICAgICAvLyB3ZSBzaG91bGQganVzdCByZXR1cm4gbm90aGluZyBpbnN0ZWFkIG9mIHJlc3VtaW5nIHRoZSBkeW5hbWljIHJlbmRlci5cbiAgICAgICAgICAgIGlmICgoaXNEZWJ1Z1N0YXRpY1NoZWxsIHx8IGlzRGVidWdEeW5hbWljQWNjZXNzZXMpICYmIHR5cGVvZiBwb3N0cG9uZWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlOiAxLFxuICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGtpbmQ6IENhY2hlZFJvdXRlS2luZC5QQUdFUyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGh0bWw6IFJlbmRlclJlc3VsdC5mcm9tU3RhdGljKCcnKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZ2VEYXRhOiB7fSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlYWRlcnM6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gSWYgdGhpcyBpcyBhIGR5bmFtaWMgcm91dGUgd2l0aCBQUFIgZW5hYmxlZCBhbmQgdGhlIGRlZmF1bHQgcm91dGVcbiAgICAgICAgICAgIC8vIG1hdGNoZXMgd2VyZSBzZXQsIHRoZW4gd2Ugc2hvdWxkIHBhc3MgdGhlIGZhbGxiYWNrIHJvdXRlIHBhcmFtcyB0b1xuICAgICAgICAgICAgLy8gdGhlIHJlbmRlcmVyIGFzIHRoaXMgaXMgYSBmYWxsYmFjayByZXZhbGlkYXRpb24gcmVxdWVzdC5cbiAgICAgICAgICAgIGNvbnN0IGZhbGxiYWNrUm91dGVQYXJhbXMgPSBwYWdlSXNEeW5hbWljICYmIGlzUm91dGVQUFJFbmFibGVkICYmIChnZXRSZXF1ZXN0TWV0YShyZXEsICdyZW5kZXJGYWxsYmFja1NoZWxsJykgfHwgaXNEZWJ1Z0ZhbGxiYWNrU2hlbGwpID8gZ2V0RmFsbGJhY2tSb3V0ZVBhcmFtcyhwYXRobmFtZSkgOiBudWxsO1xuICAgICAgICAgICAgLy8gUGVyZm9ybSB0aGUgcmVuZGVyLlxuICAgICAgICAgICAgcmV0dXJuIGRvUmVuZGVyKHtcbiAgICAgICAgICAgICAgICBzcGFuLFxuICAgICAgICAgICAgICAgIHBvc3Rwb25lZCxcbiAgICAgICAgICAgICAgICBmYWxsYmFja1JvdXRlUGFyYW1zXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgaGFuZGxlUmVzcG9uc2UgPSBhc3luYyAoc3Bhbik9PntcbiAgICAgICAgICAgIHZhciBfY2FjaGVFbnRyeV92YWx1ZSwgX2NhY2hlZERhdGFfaGVhZGVycztcbiAgICAgICAgICAgIGNvbnN0IGNhY2hlRW50cnkgPSBhd2FpdCByb3V0ZU1vZHVsZS5oYW5kbGVSZXNwb25zZSh7XG4gICAgICAgICAgICAgICAgY2FjaGVLZXk6IHNzZ0NhY2hlS2V5LFxuICAgICAgICAgICAgICAgIHJlc3BvbnNlR2VuZXJhdG9yOiAoYyk9PnJlc3BvbnNlR2VuZXJhdG9yKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNwYW4sXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5jXG4gICAgICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgIHJvdXRlS2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICAgICAgICAgIGlzT25EZW1hbmRSZXZhbGlkYXRlLFxuICAgICAgICAgICAgICAgIGlzUm91dGVQUFJFbmFibGVkLFxuICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICBuZXh0Q29uZmlnLFxuICAgICAgICAgICAgICAgIHByZXJlbmRlck1hbmlmZXN0LFxuICAgICAgICAgICAgICAgIHdhaXRVbnRpbDogY3R4LndhaXRVbnRpbFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAoaXNEcmFmdE1vZGUpIHtcbiAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKCdDYWNoZS1Db250cm9sJywgJ3ByaXZhdGUsIG5vLWNhY2hlLCBuby1zdG9yZSwgbWF4LWFnZT0wLCBtdXN0LXJldmFsaWRhdGUnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIEluIGRldiwgd2Ugc2hvdWxkIG5vdCBjYWNoZSBwYWdlcyBmb3IgYW55IHJlYXNvbi5cbiAgICAgICAgICAgIGlmIChyb3V0ZU1vZHVsZS5pc0Rldikge1xuICAgICAgICAgICAgICAgIHJlcy5zZXRIZWFkZXIoJ0NhY2hlLUNvbnRyb2wnLCAnbm8tc3RvcmUsIG11c3QtcmV2YWxpZGF0ZScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFjYWNoZUVudHJ5KSB7XG4gICAgICAgICAgICAgICAgaWYgKHNzZ0NhY2hlS2V5KSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIEEgY2FjaGUgZW50cnkgbWlnaHQgbm90IGJlIGdlbmVyYXRlZCBpZiBhIHJlc3BvbnNlIGlzIHdyaXR0ZW5cbiAgICAgICAgICAgICAgICAgICAgLy8gaW4gYGdldEluaXRpYWxQcm9wc2Agb3IgYGdldFNlcnZlclNpZGVQcm9wc2AsIGJ1dCB0aG9zZSBzaG91bGRuJ3RcbiAgICAgICAgICAgICAgICAgICAgLy8gaGF2ZSBhIGNhY2hlIGtleS4gSWYgd2UgZG8gaGF2ZSBhIGNhY2hlIGtleSBidXQgd2UgZG9uJ3QgZW5kIHVwXG4gICAgICAgICAgICAgICAgICAgIC8vIHdpdGggYSBjYWNoZSBlbnRyeSwgdGhlbiBlaXRoZXIgTmV4dC5qcyBvciB0aGUgYXBwbGljYXRpb24gaGFzIGFcbiAgICAgICAgICAgICAgICAgICAgLy8gYnVnIHRoYXQgbmVlZHMgZml4aW5nLlxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKCdpbnZhcmlhbnQ6IGNhY2hlIGVudHJ5IHJlcXVpcmVkIGJ1dCBub3QgZ2VuZXJhdGVkJyksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFwiRTYyXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoKChfY2FjaGVFbnRyeV92YWx1ZSA9IGNhY2hlRW50cnkudmFsdWUpID09IG51bGwgPyB2b2lkIDAgOiBfY2FjaGVFbnRyeV92YWx1ZS5raW5kKSAhPT0gQ2FjaGVkUm91dGVLaW5kLkFQUF9QQUdFKSB7XG4gICAgICAgICAgICAgICAgdmFyIF9jYWNoZUVudHJ5X3ZhbHVlMTtcbiAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKGBJbnZhcmlhbnQgYXBwLXBhZ2UgaGFuZGxlciByZWNlaXZlZCBpbnZhbGlkIGNhY2hlIGVudHJ5ICR7KF9jYWNoZUVudHJ5X3ZhbHVlMSA9IGNhY2hlRW50cnkudmFsdWUpID09IG51bGwgPyB2b2lkIDAgOiBfY2FjaGVFbnRyeV92YWx1ZTEua2luZH1gKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkU3MDdcIixcbiAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgZGlkUG9zdHBvbmUgPSB0eXBlb2YgY2FjaGVFbnRyeS52YWx1ZS5wb3N0cG9uZWQgPT09ICdzdHJpbmcnO1xuICAgICAgICAgICAgaWYgKGlzU1NHICYmIC8vIFdlIGRvbid0IHdhbnQgdG8gc2VuZCBhIGNhY2hlIGhlYWRlciBmb3IgcmVxdWVzdHMgdGhhdCBjb250YWluIGR5bmFtaWNcbiAgICAgICAgICAgIC8vIGRhdGEuIElmIHRoaXMgaXMgYSBEeW5hbWljIFJTQyByZXF1ZXN0IG9yIHdhc24ndCBhIFByZWZldGNoIFJTQ1xuICAgICAgICAgICAgLy8gcmVxdWVzdCwgdGhlbiB3ZSBzaG91bGQgc2V0IHRoZSBjYWNoZSBoZWFkZXIuXG4gICAgICAgICAgICAhaXNEeW5hbWljUlNDUmVxdWVzdCAmJiAoIWRpZFBvc3Rwb25lIHx8IGlzUHJlZmV0Y2hSU0NSZXF1ZXN0KSkge1xuICAgICAgICAgICAgICAgIGlmICghbWluaW1hbE1vZGUpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gc2V0IHgtbmV4dGpzLWNhY2hlIGhlYWRlciB0byBtYXRjaCB0aGUgaGVhZGVyXG4gICAgICAgICAgICAgICAgICAgIC8vIHdlIHNldCBmb3IgdGhlIGltYWdlLW9wdGltaXplclxuICAgICAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKCd4LW5leHRqcy1jYWNoZScsIGlzT25EZW1hbmRSZXZhbGlkYXRlID8gJ1JFVkFMSURBVEVEJyA6IGNhY2hlRW50cnkuaXNNaXNzID8gJ01JU1MnIDogY2FjaGVFbnRyeS5pc1N0YWxlID8gJ1NUQUxFJyA6ICdISVQnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gU2V0IGEgaGVhZGVyIHVzZWQgYnkgdGhlIGNsaWVudCByb3V0ZXIgdG8gc2lnbmFsIHRoZSByZXNwb25zZSBpcyBzdGF0aWNcbiAgICAgICAgICAgICAgICAvLyBhbmQgc2hvdWxkIHJlc3BlY3QgdGhlIGBzdGF0aWNgIGNhY2hlIHN0YWxlVGltZSB2YWx1ZS5cbiAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKE5FWFRfSVNfUFJFUkVOREVSX0hFQURFUiwgJzEnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHsgdmFsdWU6IGNhY2hlZERhdGEgfSA9IGNhY2hlRW50cnk7XG4gICAgICAgICAgICAvLyBDb2VyY2UgdGhlIGNhY2hlIGNvbnRyb2wgcGFyYW1ldGVyIGZyb20gdGhlIHJlbmRlci5cbiAgICAgICAgICAgIGxldCBjYWNoZUNvbnRyb2w7XG4gICAgICAgICAgICAvLyBJZiB0aGlzIGlzIGEgcmVzdW1lIHJlcXVlc3QgaW4gbWluaW1hbCBtb2RlIGl0IGlzIHN0cmVhbWVkIHdpdGggZHluYW1pY1xuICAgICAgICAgICAgLy8gY29udGVudCBhbmQgc2hvdWxkIG5vdCBiZSBjYWNoZWQuXG4gICAgICAgICAgICBpZiAobWluaW1hbFBvc3Rwb25lZCkge1xuICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbCA9IHtcbiAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMCxcbiAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSBlbHNlIGlmIChtaW5pbWFsTW9kZSAmJiBpc1JTQ1JlcXVlc3QgJiYgIWlzUHJlZmV0Y2hSU0NSZXF1ZXN0ICYmIGlzUm91dGVQUFJFbmFibGVkKSB7XG4gICAgICAgICAgICAgICAgY2FjaGVDb250cm9sID0ge1xuICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlOiAwLFxuICAgICAgICAgICAgICAgICAgICBleHBpcmU6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9IGVsc2UgaWYgKCFyb3V0ZU1vZHVsZS5pc0Rldikge1xuICAgICAgICAgICAgICAgIC8vIElmIHRoaXMgaXMgYSBwcmV2aWV3IG1vZGUgcmVxdWVzdCwgd2Ugc2hvdWxkbid0IGNhY2hlIGl0XG4gICAgICAgICAgICAgICAgaWYgKGlzRHJhZnRNb2RlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbCA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGU6IDAsXG4gICAgICAgICAgICAgICAgICAgICAgICBleHBpcmU6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoIWlzU1NHKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICghcmVzLmdldEhlYWRlcignQ2FjaGUtQ29udHJvbCcpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2wgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBleHBpcmU6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gSWYgdGhlIGNhY2hlIGVudHJ5IGhhcyBhIGNhY2hlIGNvbnRyb2wgd2l0aCBhIHJldmFsaWRhdGUgdmFsdWUgdGhhdCdzXG4gICAgICAgICAgICAgICAgICAgIC8vIGEgbnVtYmVyLCB1c2UgaXQuXG4gICAgICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wucmV2YWxpZGF0ZSA9PT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhciBfY2FjaGVFbnRyeV9jYWNoZUNvbnRyb2w7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wucmV2YWxpZGF0ZSA8IDEpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKGBJbnZhbGlkIHJldmFsaWRhdGUgY29uZmlndXJhdGlvbiBwcm92aWRlZDogJHtjYWNoZUVudHJ5LmNhY2hlQ29udHJvbC5yZXZhbGlkYXRlfSA8IDFgKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkUyMlwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2wgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wucmV2YWxpZGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBleHBpcmU6ICgoX2NhY2hlRW50cnlfY2FjaGVDb250cm9sID0gY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wpID09IG51bGwgPyB2b2lkIDAgOiBfY2FjaGVFbnRyeV9jYWNoZUNvbnRyb2wuZXhwaXJlKSA/PyBuZXh0Q29uZmlnLmV4cGlyZVRpbWVcbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2wgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogQ0FDSEVfT05FX1lFQVIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYWNoZUVudHJ5LmNhY2hlQ29udHJvbCA9IGNhY2hlQ29udHJvbDtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygc2VnbWVudFByZWZldGNoSGVhZGVyID09PSAnc3RyaW5nJyAmJiAoY2FjaGVkRGF0YSA9PSBudWxsID8gdm9pZCAwIDogY2FjaGVkRGF0YS5raW5kKSA9PT0gQ2FjaGVkUm91dGVLaW5kLkFQUF9QQUdFICYmIGNhY2hlZERhdGEuc2VnbWVudERhdGEpIHtcbiAgICAgICAgICAgICAgICB2YXIgX2NhY2hlZERhdGFfaGVhZGVyczE7XG4gICAgICAgICAgICAgICAgLy8gVGhpcyBpcyBhIHByZWZldGNoIHJlcXVlc3QgaXNzdWVkIGJ5IHRoZSBjbGllbnQgU2VnbWVudCBDYWNoZS4gVGhlc2VcbiAgICAgICAgICAgICAgICAvLyBzaG91bGQgbmV2ZXIgcmVhY2ggdGhlIGFwcGxpY2F0aW9uIGxheWVyIChsYW1iZGEpLiBXZSBzaG91bGQgZWl0aGVyXG4gICAgICAgICAgICAgICAgLy8gcmVzcG9uZCBmcm9tIHRoZSBjYWNoZSAoSElUKSBvciByZXNwb25kIHdpdGggMjA0IE5vIENvbnRlbnQgKE1JU1MpLlxuICAgICAgICAgICAgICAgIC8vIFNldCBhIGhlYWRlciB0byBpbmRpY2F0ZSB0aGF0IFBQUiBpcyBlbmFibGVkIGZvciB0aGlzIHJvdXRlLiBUaGlzXG4gICAgICAgICAgICAgICAgLy8gbGV0cyB0aGUgY2xpZW50IGRpc3Rpbmd1aXNoIGJldHdlZW4gYSByZWd1bGFyIGNhY2hlIG1pc3MgYW5kIGEgY2FjaGVcbiAgICAgICAgICAgICAgICAvLyBtaXNzIGR1ZSB0byBQUFIgYmVpbmcgZGlzYWJsZWQuIEluIG90aGVyIGNvbnRleHRzIHRoaXMgaGVhZGVyIGlzIHVzZWRcbiAgICAgICAgICAgICAgICAvLyB0byBpbmRpY2F0ZSB0aGF0IHRoZSByZXNwb25zZSBjb250YWlucyBkeW5hbWljIGRhdGEsIGJ1dCBoZXJlIHdlJ3JlXG4gICAgICAgICAgICAgICAgLy8gb25seSB1c2luZyBpdCB0byBpbmRpY2F0ZSB0aGF0IHRoZSBmZWF0dXJlIGlzIGVuYWJsZWQg4oCUIHRoZSBzZWdtZW50XG4gICAgICAgICAgICAgICAgLy8gcmVzcG9uc2UgaXRzZWxmIGNvbnRhaW5zIHdoZXRoZXIgdGhlIGRhdGEgaXMgZHluYW1pYy5cbiAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKE5FWFRfRElEX1BPU1RQT05FX0hFQURFUiwgJzInKTtcbiAgICAgICAgICAgICAgICAvLyBBZGQgdGhlIGNhY2hlIHRhZ3MgaGVhZGVyIHRvIHRoZSByZXNwb25zZSBpZiBpdCBleGlzdHMgYW5kIHdlJ3JlIGluXG4gICAgICAgICAgICAgICAgLy8gbWluaW1hbCBtb2RlIHdoaWxlIHJlbmRlcmluZyBhIHN0YXRpYyBwYWdlLlxuICAgICAgICAgICAgICAgIGNvbnN0IHRhZ3MgPSAoX2NhY2hlZERhdGFfaGVhZGVyczEgPSBjYWNoZWREYXRhLmhlYWRlcnMpID09IG51bGwgPyB2b2lkIDAgOiBfY2FjaGVkRGF0YV9oZWFkZXJzMVtORVhUX0NBQ0hFX1RBR1NfSEVBREVSXTtcbiAgICAgICAgICAgICAgICBpZiAobWluaW1hbE1vZGUgJiYgaXNTU0cgJiYgdGFncyAmJiB0eXBlb2YgdGFncyA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcihORVhUX0NBQ0hFX1RBR1NfSEVBREVSLCB0YWdzKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgbWF0Y2hlZFNlZ21lbnQgPSBjYWNoZWREYXRhLnNlZ21lbnREYXRhLmdldChzZWdtZW50UHJlZmV0Y2hIZWFkZXIpO1xuICAgICAgICAgICAgICAgIGlmIChtYXRjaGVkU2VnbWVudCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIENhY2hlIGhpdFxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2VuZFJlbmRlclJlc3VsdCh7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgICAgICAgICByZXMsXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAncnNjJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGdlbmVyYXRlRXRhZ3M6IG5leHRDb25maWcuZ2VuZXJhdGVFdGFncyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBvd2VyZWRCeUhlYWRlcjogbmV4dENvbmZpZy5wb3dlcmVkQnlIZWFkZXIsXG4gICAgICAgICAgICAgICAgICAgICAgICByZXN1bHQ6IFJlbmRlclJlc3VsdC5mcm9tU3RhdGljKG1hdGNoZWRTZWdtZW50KSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDogY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2xcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIENhY2hlIG1pc3MuIEVpdGhlciBhIGNhY2hlIGVudHJ5IGZvciB0aGlzIHJvdXRlIGhhcyBub3QgYmVlbiBnZW5lcmF0ZWRcbiAgICAgICAgICAgICAgICAvLyAod2hpY2ggdGVjaG5pY2FsbHkgc2hvdWxkIG5vdCBiZSBwb3NzaWJsZSB3aGVuIFBQUiBpcyBlbmFibGVkLCBiZWNhdXNlXG4gICAgICAgICAgICAgICAgLy8gYXQgYSBtaW5pbXVtIHRoZXJlIHNob3VsZCBhbHdheXMgYmUgYSBmYWxsYmFjayBlbnRyeSkgb3IgdGhlcmUncyBub1xuICAgICAgICAgICAgICAgIC8vIG1hdGNoIGZvciB0aGUgcmVxdWVzdGVkIHNlZ21lbnQuIFJlc3BvbmQgd2l0aCBhIDIwNCBObyBDb250ZW50LiBXZVxuICAgICAgICAgICAgICAgIC8vIGRvbid0IGJvdGhlciB0byByZXNwb25kIHdpdGggNDA0LCBiZWNhdXNlIHRoZXNlIHJlcXVlc3RzIGFyZSBvbmx5XG4gICAgICAgICAgICAgICAgLy8gaXNzdWVkIGFzIHBhcnQgb2YgYSBwcmVmZXRjaC5cbiAgICAgICAgICAgICAgICByZXMuc3RhdHVzQ29kZSA9IDIwNDtcbiAgICAgICAgICAgICAgICByZXR1cm4gc2VuZFJlbmRlclJlc3VsdCh7XG4gICAgICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICAgICAgcmVzLFxuICAgICAgICAgICAgICAgICAgICB0eXBlOiAncnNjJyxcbiAgICAgICAgICAgICAgICAgICAgZ2VuZXJhdGVFdGFnczogbmV4dENvbmZpZy5nZW5lcmF0ZUV0YWdzLFxuICAgICAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICByZXN1bHQ6IFJlbmRlclJlc3VsdC5mcm9tU3RhdGljKCcnKSxcbiAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sOiBjYWNoZUVudHJ5LmNhY2hlQ29udHJvbFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gSWYgdGhlcmUncyBhIGNhbGxiYWNrIGZvciBgb25DYWNoZUVudHJ5YCwgY2FsbCBpdCB3aXRoIHRoZSBjYWNoZSBlbnRyeVxuICAgICAgICAgICAgLy8gYW5kIHRoZSByZXZhbGlkYXRlIG9wdGlvbnMuXG4gICAgICAgICAgICBjb25zdCBvbkNhY2hlRW50cnkgPSBnZXRSZXF1ZXN0TWV0YShyZXEsICdvbkNhY2hlRW50cnknKTtcbiAgICAgICAgICAgIGlmIChvbkNhY2hlRW50cnkpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBmaW5pc2hlZCA9IGF3YWl0IG9uQ2FjaGVFbnRyeSh7XG4gICAgICAgICAgICAgICAgICAgIC4uLmNhY2hlRW50cnksXG4gICAgICAgICAgICAgICAgICAgIC8vIFRPRE86IHJlbW92ZSB0aGlzIHdoZW4gdXBzdHJlYW0gZG9lc24ndFxuICAgICAgICAgICAgICAgICAgICAvLyBhbHdheXMgZXhwZWN0IHRoaXMgdmFsdWUgdG8gYmUgXCJQQUdFXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLmNhY2hlRW50cnkudmFsdWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBraW5kOiAnUEFHRSdcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0sIHtcbiAgICAgICAgICAgICAgICAgICAgdXJsOiBnZXRSZXF1ZXN0TWV0YShyZXEsICdpbml0VVJMJylcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBpZiAoZmluaXNoZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gVE9ETzogbWF5YmUgd2UgaGF2ZSB0byBlbmQgdGhlIHJlcXVlc3Q/XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIHRoZSByZXF1ZXN0IGhhcyBhIHBvc3Rwb25lZCBzdGF0ZSBhbmQgaXQncyBhIHJlc3VtZSByZXF1ZXN0IHdlXG4gICAgICAgICAgICAvLyBzaG91bGQgZXJyb3IuXG4gICAgICAgICAgICBpZiAoZGlkUG9zdHBvbmUgJiYgbWluaW1hbFBvc3Rwb25lZCkge1xuICAgICAgICAgICAgICAgIHRocm93IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgRXJyb3IoJ0ludmFyaWFudDogcG9zdHBvbmVkIHN0YXRlIHNob3VsZCBub3QgYmUgcHJlc2VudCBvbiBhIHJlc3VtZSByZXF1ZXN0JyksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFMzk2XCIsXG4gICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChjYWNoZWREYXRhLmhlYWRlcnMpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBoZWFkZXJzID0ge1xuICAgICAgICAgICAgICAgICAgICAuLi5jYWNoZWREYXRhLmhlYWRlcnNcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIGlmICghbWluaW1hbE1vZGUgfHwgIWlzU1NHKSB7XG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSBoZWFkZXJzW05FWFRfQ0FDSEVfVEFHU19IRUFERVJdO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBmb3IgKGxldCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoaGVhZGVycykpe1xuICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAndW5kZWZpbmVkJykgY29udGludWU7XG4gICAgICAgICAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCB2IG9mIHZhbHVlKXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXMuYXBwZW5kSGVhZGVyKGtleSwgdik7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlID09PSAnbnVtYmVyJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSB2YWx1ZS50b1N0cmluZygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVzLmFwcGVuZEhlYWRlcihrZXksIHZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcy5hcHBlbmRIZWFkZXIoa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBBZGQgdGhlIGNhY2hlIHRhZ3MgaGVhZGVyIHRvIHRoZSByZXNwb25zZSBpZiBpdCBleGlzdHMgYW5kIHdlJ3JlIGluXG4gICAgICAgICAgICAvLyBtaW5pbWFsIG1vZGUgd2hpbGUgcmVuZGVyaW5nIGEgc3RhdGljIHBhZ2UuXG4gICAgICAgICAgICBjb25zdCB0YWdzID0gKF9jYWNoZWREYXRhX2hlYWRlcnMgPSBjYWNoZWREYXRhLmhlYWRlcnMpID09IG51bGwgPyB2b2lkIDAgOiBfY2FjaGVkRGF0YV9oZWFkZXJzW05FWFRfQ0FDSEVfVEFHU19IRUFERVJdO1xuICAgICAgICAgICAgaWYgKG1pbmltYWxNb2RlICYmIGlzU1NHICYmIHRhZ3MgJiYgdHlwZW9mIHRhZ3MgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcihORVhUX0NBQ0hFX1RBR1NfSEVBREVSLCB0YWdzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIHRoZSByZXF1ZXN0IGlzIGEgZGF0YSByZXF1ZXN0LCB0aGVuIHdlIHNob3VsZG4ndCBzZXQgdGhlIHN0YXR1cyBjb2RlXG4gICAgICAgICAgICAvLyBmcm9tIHRoZSByZXNwb25zZSBiZWNhdXNlIGl0IHNob3VsZCBhbHdheXMgYmUgMjAwLiBUaGlzIHNob3VsZCBiZSBnYXRlZFxuICAgICAgICAgICAgLy8gYmVoaW5kIHRoZSBleHBlcmltZW50YWwgUFBSIGZsYWcuXG4gICAgICAgICAgICBpZiAoY2FjaGVkRGF0YS5zdGF0dXMgJiYgKCFpc1JTQ1JlcXVlc3QgfHwgIWlzUm91dGVQUFJFbmFibGVkKSkge1xuICAgICAgICAgICAgICAgIHJlcy5zdGF0dXNDb2RlID0gY2FjaGVkRGF0YS5zdGF0dXM7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBSZWRpcmVjdCBpbmZvcm1hdGlvbiBpcyBlbmNvZGVkIGluIFJTQyBwYXlsb2FkLCBzbyB3ZSBkb24ndCBuZWVkIHRvIHVzZSByZWRpcmVjdCBzdGF0dXMgY29kZXNcbiAgICAgICAgICAgIGlmICghbWluaW1hbE1vZGUgJiYgY2FjaGVkRGF0YS5zdGF0dXMgJiYgUmVkaXJlY3RTdGF0dXNDb2RlW2NhY2hlZERhdGEuc3RhdHVzXSAmJiBpc1JTQ1JlcXVlc3QpIHtcbiAgICAgICAgICAgICAgICByZXMuc3RhdHVzQ29kZSA9IDIwMDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIE1hcmsgdGhhdCB0aGUgcmVxdWVzdCBkaWQgcG9zdHBvbmUuXG4gICAgICAgICAgICBpZiAoZGlkUG9zdHBvbmUpIHtcbiAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKE5FWFRfRElEX1BPU1RQT05FX0hFQURFUiwgJzEnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIHdlIGRvbid0IGdvIHRocm91Z2ggdGhpcyBibG9jayB3aGVuIHByZXZpZXcgbW9kZSBpcyB0cnVlXG4gICAgICAgICAgICAvLyBhcyBwcmV2aWV3IG1vZGUgaXMgYSBkeW5hbWljIHJlcXVlc3QgKGJ5cGFzc2VzIGNhY2hlKSBhbmQgZG9lc24ndFxuICAgICAgICAgICAgLy8gZ2VuZXJhdGUgYm90aCBIVE1MIGFuZCBwYXlsb2FkcyBpbiB0aGUgc2FtZSByZXF1ZXN0IHNvIGNvbnRpbnVlIHRvIGp1c3RcbiAgICAgICAgICAgIC8vIHJldHVybiB0aGUgZ2VuZXJhdGVkIHBheWxvYWRcbiAgICAgICAgICAgIGlmIChpc1JTQ1JlcXVlc3QgJiYgIWlzRHJhZnRNb2RlKSB7XG4gICAgICAgICAgICAgICAgLy8gSWYgdGhpcyBpcyBhIGR5bmFtaWMgUlNDIHJlcXVlc3QsIHRoZW4gc3RyZWFtIHRoZSByZXNwb25zZS5cbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGNhY2hlZERhdGEucnNjRGF0YSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGNhY2hlZERhdGEucG9zdHBvbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKCdJbnZhcmlhbnQ6IEV4cGVjdGVkIHBvc3Rwb25lZCB0byBiZSB1bmRlZmluZWQnKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFwiRTM3MlwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHNlbmRSZW5kZXJSZXN1bHQoe1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzLFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3JzYycsXG4gICAgICAgICAgICAgICAgICAgICAgICBnZW5lcmF0ZUV0YWdzOiBuZXh0Q29uZmlnLmdlbmVyYXRlRXRhZ3MsXG4gICAgICAgICAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiBjYWNoZWREYXRhLmh0bWwsXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBEeW5hbWljIFJTQyByZXNwb25zZXMgY2Fubm90IGJlIGNhY2hlZCwgZXZlbiBpZiB0aGV5J3JlXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBjb25maWd1cmVkIHdpdGggYGZvcmNlLXN0YXRpY2AgYmVjYXVzZSB3ZSBoYXZlIG5vIHdheSBvZlxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gZGlzdGluZ3Vpc2hpbmcgYmV0d2VlbiBgZm9yY2Utc3RhdGljYCBhbmQgcGFnZXMgdGhhdCBoYXZlIG5vXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBwb3N0cG9uZWQgc3RhdGUuXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBUT0RPOiBkaXN0aW5ndWlzaCBgZm9yY2Utc3RhdGljYCBmcm9tIHBhZ2VzIHdpdGggbm8gcG9zdHBvbmVkIHN0YXRlIChzdGF0aWMpXG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2w6IGlzRHluYW1pY1JTQ1JlcXVlc3QgPyB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBleHBpcmU6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgICAgICAgICAgfSA6IGNhY2hlRW50cnkuY2FjaGVDb250cm9sXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyBBcyB0aGlzIGlzbid0IGEgcHJlZmV0Y2ggcmVxdWVzdCwgd2Ugc2hvdWxkIHNlcnZlIHRoZSBzdGF0aWMgZmxpZ2h0XG4gICAgICAgICAgICAgICAgLy8gZGF0YS5cbiAgICAgICAgICAgICAgICByZXR1cm4gc2VuZFJlbmRlclJlc3VsdCh7XG4gICAgICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICAgICAgcmVzLFxuICAgICAgICAgICAgICAgICAgICB0eXBlOiAncnNjJyxcbiAgICAgICAgICAgICAgICAgICAgZ2VuZXJhdGVFdGFnczogbmV4dENvbmZpZy5nZW5lcmF0ZUV0YWdzLFxuICAgICAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICByZXN1bHQ6IFJlbmRlclJlc3VsdC5mcm9tU3RhdGljKGNhY2hlZERhdGEucnNjRGF0YSksXG4gICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDogY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2xcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIFRoaXMgaXMgYSByZXF1ZXN0IGZvciBIVE1MIGRhdGEuXG4gICAgICAgICAgICBsZXQgYm9keSA9IGNhY2hlZERhdGEuaHRtbDtcbiAgICAgICAgICAgIC8vIElmIHRoZXJlJ3Mgbm8gcG9zdHBvbmVkIHN0YXRlLCB3ZSBzaG91bGQganVzdCBzZXJ2ZSB0aGUgSFRNTC4gVGhpc1xuICAgICAgICAgICAgLy8gc2hvdWxkIGFsc28gYmUgdGhlIGNhc2UgZm9yIGEgcmVzdW1lIHJlcXVlc3QgYmVjYXVzZSBpdCdzIGNvbXBsZXRlZFxuICAgICAgICAgICAgLy8gYXMgYSBzZXJ2ZXIgcmVuZGVyIChyYXRoZXIgdGhhbiBhIHN0YXRpYyByZW5kZXIpLlxuICAgICAgICAgICAgaWYgKCFkaWRQb3N0cG9uZSB8fCBtaW5pbWFsTW9kZSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBzZW5kUmVuZGVyUmVzdWx0KHtcbiAgICAgICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgICAgICByZXMsXG4gICAgICAgICAgICAgICAgICAgIHR5cGU6ICdodG1sJyxcbiAgICAgICAgICAgICAgICAgICAgZ2VuZXJhdGVFdGFnczogbmV4dENvbmZpZy5nZW5lcmF0ZUV0YWdzLFxuICAgICAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICByZXN1bHQ6IGJvZHksXG4gICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDogY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2xcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIHdlJ3JlIGRlYnVnZ2luZyB0aGUgc3RhdGljIHNoZWxsIG9yIHRoZSBkeW5hbWljIEFQSSBhY2Nlc3Nlcywgd2VcbiAgICAgICAgICAgIC8vIHNob3VsZCBqdXN0IHNlcnZlIHRoZSBIVE1MIHdpdGhvdXQgcmVzdW1pbmcgdGhlIHJlbmRlci4gVGhlIHJldHVybmVkXG4gICAgICAgICAgICAvLyBIVE1MIHdpbGwgYmUgdGhlIHN0YXRpYyBzaGVsbCBzbyBhbGwgdGhlIER5bmFtaWMgQVBJJ3Mgd2lsbCBiZSB1c2VkXG4gICAgICAgICAgICAvLyBkdXJpbmcgc3RhdGljIGdlbmVyYXRpb24uXG4gICAgICAgICAgICBpZiAoaXNEZWJ1Z1N0YXRpY1NoZWxsIHx8IGlzRGVidWdEeW5hbWljQWNjZXNzZXMpIHtcbiAgICAgICAgICAgICAgICAvLyBTaW5jZSB3ZSdyZSBub3QgcmVzdW1pbmcgdGhlIHJlbmRlciwgd2UgbmVlZCB0byBhdCBsZWFzdCBhZGQgdGhlXG4gICAgICAgICAgICAgICAgLy8gY2xvc2luZyBib2R5IGFuZCBodG1sIHRhZ3MgdG8gY3JlYXRlIHZhbGlkIEhUTUwuXG4gICAgICAgICAgICAgICAgYm9keS5jaGFpbihuZXcgUmVhZGFibGVTdHJlYW0oe1xuICAgICAgICAgICAgICAgICAgICBzdGFydCAoY29udHJvbGxlcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbGxlci5lbnF1ZXVlKEVOQ09ERURfVEFHUy5DTE9TRUQuQk9EWV9BTkRfSFRNTCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb250cm9sbGVyLmNsb3NlKCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHNlbmRSZW5kZXJSZXN1bHQoe1xuICAgICAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgICAgIHJlcyxcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2h0bWwnLFxuICAgICAgICAgICAgICAgICAgICBnZW5lcmF0ZUV0YWdzOiBuZXh0Q29uZmlnLmdlbmVyYXRlRXRhZ3MsXG4gICAgICAgICAgICAgICAgICAgIHBvd2VyZWRCeUhlYWRlcjogbmV4dENvbmZpZy5wb3dlcmVkQnlIZWFkZXIsXG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdDogYm9keSxcbiAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlOiAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gVGhpcyByZXF1ZXN0IGhhcyBwb3N0cG9uZWQsIHNvIGxldCdzIGNyZWF0ZSBhIG5ldyB0cmFuc2Zvcm1lciB0aGF0IHRoZVxuICAgICAgICAgICAgLy8gZHluYW1pYyBkYXRhIGNhbiBwaXBlIHRvIHRoYXQgd2lsbCBhdHRhY2ggdGhlIGR5bmFtaWMgZGF0YSB0byB0aGUgZW5kXG4gICAgICAgICAgICAvLyBvZiB0aGUgcmVzcG9uc2UuXG4gICAgICAgICAgICBjb25zdCB0cmFuc2Zvcm1lciA9IG5ldyBUcmFuc2Zvcm1TdHJlYW0oKTtcbiAgICAgICAgICAgIGJvZHkuY2hhaW4odHJhbnNmb3JtZXIucmVhZGFibGUpO1xuICAgICAgICAgICAgLy8gUGVyZm9ybSB0aGUgcmVuZGVyIGFnYWluLCBidXQgdGhpcyB0aW1lLCBwcm92aWRlIHRoZSBwb3N0cG9uZWQgc3RhdGUuXG4gICAgICAgICAgICAvLyBXZSBkb24ndCBhd2FpdCBiZWNhdXNlIHdlIHdhbnQgdGhlIHJlc3VsdCB0byBzdGFydCBzdHJlYW1pbmcgbm93LCBhbmRcbiAgICAgICAgICAgIC8vIHdlJ3ZlIGFscmVhZHkgY2hhaW5lZCB0aGUgdHJhbnNmb3JtZXIncyByZWFkYWJsZSB0byB0aGUgcmVuZGVyIHJlc3VsdC5cbiAgICAgICAgICAgIGRvUmVuZGVyKHtcbiAgICAgICAgICAgICAgICBzcGFuLFxuICAgICAgICAgICAgICAgIHBvc3Rwb25lZDogY2FjaGVkRGF0YS5wb3N0cG9uZWQsXG4gICAgICAgICAgICAgICAgLy8gVGhpcyBpcyBhIHJlc3VtZSByZW5kZXIsIG5vdCBhIGZhbGxiYWNrIHJlbmRlciwgc28gd2UgZG9uJ3QgbmVlZCB0b1xuICAgICAgICAgICAgICAgIC8vIHNldCB0aGlzLlxuICAgICAgICAgICAgICAgIGZhbGxiYWNrUm91dGVQYXJhbXM6IG51bGxcbiAgICAgICAgICAgIH0pLnRoZW4oYXN5bmMgKHJlc3VsdCk9PntcbiAgICAgICAgICAgICAgICB2YXIgX3Jlc3VsdF92YWx1ZTtcbiAgICAgICAgICAgICAgICBpZiAoIXJlc3VsdCkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKCdJbnZhcmlhbnQ6IGV4cGVjdGVkIGEgcmVzdWx0IHRvIGJlIHJldHVybmVkJyksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFwiRTQ2M1wiLFxuICAgICAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICgoKF9yZXN1bHRfdmFsdWUgPSByZXN1bHQudmFsdWUpID09IG51bGwgPyB2b2lkIDAgOiBfcmVzdWx0X3ZhbHVlLmtpbmQpICE9PSBDYWNoZWRSb3V0ZUtpbmQuQVBQX1BBR0UpIHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIF9yZXN1bHRfdmFsdWUxO1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKGBJbnZhcmlhbnQ6IGV4cGVjdGVkIGEgcGFnZSByZXNwb25zZSwgZ290ICR7KF9yZXN1bHRfdmFsdWUxID0gcmVzdWx0LnZhbHVlKSA9PSBudWxsID8gdm9pZCAwIDogX3Jlc3VsdF92YWx1ZTEua2luZH1gKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFMzA1XCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gUGlwZSB0aGUgcmVzdW1lIHJlc3VsdCB0byB0aGUgdHJhbnNmb3JtZXIuXG4gICAgICAgICAgICAgICAgYXdhaXQgcmVzdWx0LnZhbHVlLmh0bWwucGlwZVRvKHRyYW5zZm9ybWVyLndyaXRhYmxlKTtcbiAgICAgICAgICAgIH0pLmNhdGNoKChlcnIpPT57XG4gICAgICAgICAgICAgICAgLy8gQW4gZXJyb3Igb2NjdXJyZWQgZHVyaW5nIHBpcGluZyBvciBwcmVwYXJpbmcgdGhlIHJlbmRlciwgYWJvcnRcbiAgICAgICAgICAgICAgICAvLyB0aGUgdHJhbnNmb3JtZXJzIHdyaXRlciBzbyB3ZSBjYW4gdGVybWluYXRlIHRoZSBzdHJlYW0uXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtZXIud3JpdGFibGUuYWJvcnQoZXJyKS5jYXRjaCgoZSk9PntcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcImNvdWxkbid0IGFib3J0IHRyYW5zZm9ybWVyXCIsIGUpO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gc2VuZFJlbmRlclJlc3VsdCh7XG4gICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgIHJlcyxcbiAgICAgICAgICAgICAgICB0eXBlOiAnaHRtbCcsXG4gICAgICAgICAgICAgICAgZ2VuZXJhdGVFdGFnczogbmV4dENvbmZpZy5nZW5lcmF0ZUV0YWdzLFxuICAgICAgICAgICAgICAgIHBvd2VyZWRCeUhlYWRlcjogbmV4dENvbmZpZy5wb3dlcmVkQnlIZWFkZXIsXG4gICAgICAgICAgICAgICAgcmVzdWx0OiBib2R5LFxuICAgICAgICAgICAgICAgIC8vIFdlIGRvbid0IHdhbnQgdG8gY2FjaGUgdGhlIHJlc3BvbnNlIGlmIGl0IGhhcyBwb3N0cG9uZWQgZGF0YSBiZWNhdXNlXG4gICAgICAgICAgICAgICAgLy8gdGhlIHJlc3BvbnNlIGJlaW5nIHNlbnQgdG8gdGhlIGNsaWVudCBpdCdzIGR5bmFtaWMgcGFydHMgYXJlIHN0cmVhbWVkXG4gICAgICAgICAgICAgICAgLy8gdG8gdGhlIGNsaWVudCBvbiB0aGUgc2FtZSByZXF1ZXN0LlxuICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDoge1xuICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlOiAwLFxuICAgICAgICAgICAgICAgICAgICBleHBpcmU6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgICAgICAvLyBUT0RPOiBhY3RpdmVTcGFuIGNvZGUgcGF0aCBpcyBmb3Igd2hlbiB3cmFwcGVkIGJ5XG4gICAgICAgIC8vIG5leHQtc2VydmVyIGNhbiBiZSByZW1vdmVkIHdoZW4gdGhpcyBpcyBubyBsb25nZXIgdXNlZFxuICAgICAgICBpZiAoYWN0aXZlU3Bhbikge1xuICAgICAgICAgICAgYXdhaXQgaGFuZGxlUmVzcG9uc2UoYWN0aXZlU3Bhbik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgdHJhY2VyLndpdGhQcm9wYWdhdGVkQ29udGV4dChyZXEuaGVhZGVycywgKCk9PnRyYWNlci50cmFjZShCYXNlU2VydmVyU3Bhbi5oYW5kbGVSZXF1ZXN0LCB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW5OYW1lOiBgJHttZXRob2R9ICR7cmVxLnVybH1gLFxuICAgICAgICAgICAgICAgICAgICBraW5kOiBTcGFuS2luZC5TRVJWRVIsXG4gICAgICAgICAgICAgICAgICAgIGF0dHJpYnV0ZXM6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICdodHRwLm1ldGhvZCc6IG1ldGhvZCxcbiAgICAgICAgICAgICAgICAgICAgICAgICdodHRwLnRhcmdldCc6IHJlcS51cmxcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0sIGhhbmRsZVJlc3BvbnNlKSk7XG4gICAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgLy8gaWYgd2UgYXJlbid0IHdyYXBwZWQgYnkgYmFzZS1zZXJ2ZXIgaGFuZGxlIGhlcmVcbiAgICAgICAgaWYgKCFhY3RpdmVTcGFuICYmICEoZXJyIGluc3RhbmNlb2YgTm9GYWxsYmFja0Vycm9yKSkge1xuICAgICAgICAgICAgYXdhaXQgcm91dGVNb2R1bGUub25SZXF1ZXN0RXJyb3IocmVxLCBlcnIsIHtcbiAgICAgICAgICAgICAgICByb3V0ZXJLaW5kOiAnQXBwIFJvdXRlcicsXG4gICAgICAgICAgICAgICAgcm91dGVQYXRoOiBzcmNQYWdlLFxuICAgICAgICAgICAgICAgIHJvdXRlVHlwZTogJ3JlbmRlcicsXG4gICAgICAgICAgICAgICAgcmV2YWxpZGF0ZVJlYXNvbjogZ2V0UmV2YWxpZGF0ZVJlYXNvbih7XG4gICAgICAgICAgICAgICAgICAgIGlzUmV2YWxpZGF0ZTogaXNTU0csXG4gICAgICAgICAgICAgICAgICAgIGlzT25EZW1hbmRSZXZhbGlkYXRlXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIH0sIHJvdXRlclNlcnZlckNvbnRleHQpO1xuICAgICAgICB9XG4gICAgICAgIC8vIHJldGhyb3cgc28gdGhhdCB3ZSBjYW4gaGFuZGxlIHNlcnZpbmcgZXJyb3IgcGFnZVxuICAgICAgICB0aHJvdyBlcnI7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2F%5Bid%5D%2Fpage&page=%2Fchat%2F%5Bid%5D%2Fpage&appPaths=%2Fchat%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fchat%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cdata%5Cstudy%5Cpython%5Crag%5Crag-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdata%5Cstudy%5Cpython%5Crag%5Crag-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/[id]/page.tsx */ \"(rsc)/./src/app/chat/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjZfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNkYXRhJTVDJTVDc3R1ZHklNUMlNUNweXRob24lNUMlNUNyYWclNUMlNUNyYWctZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNjaGF0JTVDJTVDJTVCaWQlNUQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQStHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxkYXRhXFxcXHN0dWR5XFxcXHB5dGhvblxcXFxyYWdcXFxccmFnLWZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcY2hhdFxcXFxbaWRdXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/layout.tsx */ \"(rsc)/./src/app/chat/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjZfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNkYXRhJTVDJTVDc3R1ZHklNUMlNUNweXRob24lNUMlNUNyYWclNUMlNUNyYWctZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNjaGF0JTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQTJHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxkYXRhXFxcXHN0dWR5XFxcXHB5dGhvblxcXFxyYWdcXFxccmFnLWZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcY2hhdFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjZfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxkYXRhXFxzdHVkeVxccHl0aG9uXFxyYWdcXHJhZy1mcm9udGVuZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/chat/[id]/page.tsx":
/*!************************************!*\
  !*** ./src/app/chat/[id]/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\data\\study\\python\\rag\\rag-frontend\\src\\app\\chat\\[id]\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/chat/layout.tsx":
/*!*********************************!*\
  !*** ./src/app/chat/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\data\\study\\python\\rag\\rag-frontend\\src\\app\\chat\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2d70dee09fd2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcZGF0YVxcc3R1ZHlcXHB5dGhvblxccmFnXFxyYWctZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjJkNzBkZWUwOWZkMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"AI智能助手\",\n    description: \"基于RAG的多模态AI应用平台\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1QsK0pBQWU7c0JBQzlCLDRFQUFDVTtnQkFBSUQsV0FBVTswQkFDWko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsiRDpcXGRhdGFcXHN0dWR5XFxweXRob25cXHJhZ1xccmFnLWZyb250ZW5kXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJBSeaZuuiDveWKqeaJi1wiLFxuICBkZXNjcmlwdGlvbjogXCLln7rkuo5SQUfnmoTlpJrmqKHmgIFBSeW6lOeUqOW5s+WPsFwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJkaXYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/[id]/page.tsx */ \"(ssr)/./src/app/chat/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjZfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNkYXRhJTVDJTVDc3R1ZHklNUMlNUNweXRob24lNUMlNUNyYWclNUMlNUNyYWctZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNjaGF0JTVDJTVDJTVCaWQlNUQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQStHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxkYXRhXFxcXHN0dWR5XFxcXHB5dGhvblxcXFxyYWdcXFxccmFnLWZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcY2hhdFxcXFxbaWRdXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/layout.tsx */ \"(ssr)/./src/app/chat/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS40LjZfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNkYXRhJTVDJTVDc3R1ZHklNUMlNUNweXRob24lNUMlNUNyYWclNUMlNUNyYWctZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNjaGF0JTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQTJHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxkYXRhXFxcXHN0dWR5XFxcXHB5dGhvblxcXFxyYWdcXFxccmFnLWZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcY2hhdFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cdata%5C%5Cstudy%5C%5Cpython%5C%5Crag%5C%5Crag-frontend%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/chat/[id]/page.tsx":
/*!************************************!*\
  !*** ./src/app/chat/[id]/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _components_chat_ChatMessages__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/ChatMessages */ \"(ssr)/./src/components/chat/ChatMessages.tsx\");\n/* harmony import */ var _components_chat_ChatInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/chat/ChatInput */ \"(ssr)/./src/components/chat/ChatInput.tsx\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction ChatPage() {\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const conversationId = params.id;\n    const [conversation, setConversation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const { setConversationTitle } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_6__.useChatStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            if (!conversationId) return;\n            const loadConversationAndMessages = {\n                \"ChatPage.useEffect.loadConversationAndMessages\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // 通过历史接口同时获取对话信息和消息\n                        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.chatApi.getConversationHistory(conversationId);\n                        if (response.success && response.data) {\n                            setConversation(response.data.conversation);\n                            setConversationTitle(response.data.conversation.title);\n                            // 将qa_pairs转换为Message格式\n                            const messages = [];\n                            response.data.qa_pairs.forEach({\n                                \"ChatPage.useEffect.loadConversationAndMessages\": (qa)=>{\n                                    // 添加用户消息\n                                    messages.push({\n                                        id: qa.id + '_user',\n                                        content: qa.query,\n                                        role: 'user',\n                                        timestamp: qa.created_at,\n                                        conversationId: conversationId\n                                    });\n                                    // 添加助手回复\n                                    messages.push({\n                                        id: qa.id + '_assistant',\n                                        content: qa.answer,\n                                        role: 'assistant',\n                                        timestamp: qa.created_at,\n                                        conversationId: conversationId,\n                                        metadata: qa.metadata\n                                    });\n                                }\n                            }[\"ChatPage.useEffect.loadConversationAndMessages\"]);\n                            // 按时间排序消息\n                            messages.sort({\n                                \"ChatPage.useEffect.loadConversationAndMessages\": (a, b)=>{\n                                    const timeA = typeof a.timestamp === 'string' ? new Date(a.timestamp) : a.timestamp;\n                                    const timeB = typeof b.timestamp === 'string' ? new Date(b.timestamp) : b.timestamp;\n                                    return timeA.getTime() - timeB.getTime();\n                                }\n                            }[\"ChatPage.useEffect.loadConversationAndMessages\"]);\n                            setMessages(messages);\n                        }\n                    } catch (error) {\n                        console.error('加载对话失败:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ChatPage.useEffect.loadConversationAndMessages\"];\n            loadConversationAndMessages();\n            return ({\n                \"ChatPage.useEffect\": ()=>{\n                    setConversationTitle(null);\n                }\n            })[\"ChatPage.useEffect\"];\n        }\n    }[\"ChatPage.useEffect\"], [\n        conversationId,\n        setConversationTitle\n    ]);\n    // 处理初始消息（从sessionStorage获取）\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            if (conversation && messages.length === 0) {\n                const initialMessage = sessionStorage.getItem(`initialMessage_${conversationId}`);\n                if (initialMessage) {\n                    // 发送初始消息\n                    handleSendMessage(initialMessage).then({\n                        \"ChatPage.useEffect\": ()=>{\n                            sessionStorage.removeItem(`initialMessage_${conversationId}`);\n                        }\n                    }[\"ChatPage.useEffect\"]);\n                }\n            }\n        }\n    }[\"ChatPage.useEffect\"], [\n        conversation,\n        messages.length,\n        conversationId\n    ]);\n    const handleSendMessage = async (message)=>{\n        if (!message.trim() || !conversation) return;\n        // 添加用户消息到界面\n        const userMessage = {\n            id: Date.now().toString(),\n            content: message,\n            role: 'user',\n            timestamp: new Date(),\n            conversationId: conversation.id\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        // 创建一个临时的助手消息用于流式更新\n        const tempAssistantId = 'temp_' + Date.now();\n        const assistantMessage = {\n            id: tempAssistantId,\n            content: '',\n            role: 'assistant',\n            timestamp: new Date(),\n            conversationId: conversation.id\n        };\n        setMessages((prev)=>[\n                ...prev,\n                assistantMessage\n            ]);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_3__.chatApi.sendMessageStream(conversationId, {\n                message: message\n            }, (chunk)=>{\n                switch(chunk.type){\n                    case 'start':\n                        break;\n                    case 'content':\n                        // 更新助手消息内容（直接替换，因为后端返回的是完整文本）\n                        if (chunk.content) {\n                            setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantId ? {\n                                        ...msg,\n                                        content: chunk.content || ''\n                                    } : msg));\n                        }\n                        break;\n                    case 'done':\n                        // 流式完成，更新消息ID和元数据\n                        setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantId ? {\n                                    ...msg,\n                                    id: chunk.qa_pair_id || tempAssistantId,\n                                    metadata: chunk.metadata\n                                } : msg));\n                        break;\n                    case 'error':\n                        // 处理错误\n                        console.error('流式响应错误:', chunk.error);\n                        setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantId ? {\n                                    ...msg,\n                                    content: '抱歉，发生了错误：' + chunk.error\n                                } : msg));\n                        break;\n                }\n            });\n        } catch (error) {\n            console.error('发送消息失败:', error);\n            // 更新错误消息\n            setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantId ? {\n                        ...msg,\n                        content: '抱歉，发送消息失败，请重试。'\n                    } : msg));\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\",\n                        style: {\n                            animationDelay: '0.1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\",\n                        style: {\n                            animationDelay: '0.2s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this);\n    }\n    if (!conversation) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"对话不存在\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"请检查链接是否正确\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 187,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 max-w-3xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatMessages__WEBPACK_IMPORTED_MODULE_4__.ChatMessages, {\n                    messages: messages\n                }, void 0, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sticky bottom-0 bg-white/90 backdrop-blur-xl border-t border-gray-100/50 px-6 py-6 flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-3xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatInput__WEBPACK_IMPORTED_MODULE_5__.ChatInput, {\n                        onSend: handleSendMessage\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2NoYXQvW2lkXS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNBO0FBQ1I7QUFFMEI7QUFDTjtBQUNiO0FBRTVCLFNBQVNPO0lBQ3RCLE1BQU1DLFNBQVNSLDBEQUFTQTtJQUN4QixNQUFNUyxpQkFBaUJELE9BQU9FLEVBQUU7SUFFaEMsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR1YsK0NBQVFBLENBQXNCO0lBQ3RFLE1BQU0sQ0FBQ1csVUFBVUMsWUFBWSxHQUFHWiwrQ0FBUUEsQ0FBWSxFQUFFO0lBQ3RELE1BQU0sQ0FBQ2EsU0FBU0MsV0FBVyxHQUFHZCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLEVBQUVlLG9CQUFvQixFQUFFLEdBQUdYLHdEQUFZQTtJQUU3Q0wsZ0RBQVNBOzhCQUFDO1lBQ1IsSUFBSSxDQUFDUSxnQkFBZ0I7WUFFckIsTUFBTVM7a0VBQThCO29CQUNsQyxJQUFJO3dCQUNGRixXQUFXO3dCQUVYLG9CQUFvQjt3QkFDcEIsTUFBTUcsV0FBVyxNQUFNaEIsNkNBQU9BLENBQUNpQixzQkFBc0IsQ0FBQ1g7d0JBRXRELElBQUlVLFNBQVNFLE9BQU8sSUFBSUYsU0FBU0csSUFBSSxFQUFFOzRCQUNyQ1YsZ0JBQWdCTyxTQUFTRyxJQUFJLENBQUNYLFlBQVk7NEJBQzFDTSxxQkFBcUJFLFNBQVNHLElBQUksQ0FBQ1gsWUFBWSxDQUFDWSxLQUFLOzRCQUVyRCx3QkFBd0I7NEJBQ3hCLE1BQU1WLFdBQXNCLEVBQUU7NEJBQzlCTSxTQUFTRyxJQUFJLENBQUNFLFFBQVEsQ0FBQ0MsT0FBTztrRkFBQ0MsQ0FBQUE7b0NBQzdCLFNBQVM7b0NBQ1RiLFNBQVNjLElBQUksQ0FBQzt3Q0FDWmpCLElBQUlnQixHQUFHaEIsRUFBRSxHQUFHO3dDQUNaa0IsU0FBU0YsR0FBR0csS0FBSzt3Q0FDakJDLE1BQU07d0NBQ05DLFdBQVdMLEdBQUdNLFVBQVU7d0NBQ3hCdkIsZ0JBQWdCQTtvQ0FDbEI7b0NBQ0EsU0FBUztvQ0FDVEksU0FBU2MsSUFBSSxDQUFDO3dDQUNaakIsSUFBSWdCLEdBQUdoQixFQUFFLEdBQUc7d0NBQ1prQixTQUFTRixHQUFHTyxNQUFNO3dDQUNsQkgsTUFBTTt3Q0FDTkMsV0FBV0wsR0FBR00sVUFBVTt3Q0FDeEJ2QixnQkFBZ0JBO3dDQUNoQnlCLFVBQVVSLEdBQUdRLFFBQVE7b0NBQ3ZCO2dDQUNGOzs0QkFFQSxVQUFVOzRCQUNWckIsU0FBU3NCLElBQUk7a0ZBQUMsQ0FBQ0MsR0FBR0M7b0NBQ2hCLE1BQU1DLFFBQVEsT0FBT0YsRUFBRUwsU0FBUyxLQUFLLFdBQVcsSUFBSVEsS0FBS0gsRUFBRUwsU0FBUyxJQUFJSyxFQUFFTCxTQUFTO29DQUNuRixNQUFNUyxRQUFRLE9BQU9ILEVBQUVOLFNBQVMsS0FBSyxXQUFXLElBQUlRLEtBQUtGLEVBQUVOLFNBQVMsSUFBSU0sRUFBRU4sU0FBUztvQ0FDbkYsT0FBT08sTUFBTUcsT0FBTyxLQUFLRCxNQUFNQyxPQUFPO2dDQUN4Qzs7NEJBRUEzQixZQUFZRDt3QkFDZDtvQkFDRixFQUFFLE9BQU82QixPQUFPO3dCQUNkQyxRQUFRRCxLQUFLLENBQUMsV0FBV0E7b0JBQzNCLFNBQVU7d0JBQ1IxQixXQUFXO29CQUNiO2dCQUNGOztZQUVBRTtZQUVBO3NDQUFPO29CQUNMRCxxQkFBcUI7Z0JBQ3ZCOztRQUNGOzZCQUFHO1FBQUNSO1FBQWdCUTtLQUFxQjtJQUV6Qyw0QkFBNEI7SUFDNUJoQixnREFBU0E7OEJBQUM7WUFDUixJQUFJVSxnQkFBZ0JFLFNBQVMrQixNQUFNLEtBQUssR0FBRztnQkFDekMsTUFBTUMsaUJBQWlCQyxlQUFlQyxPQUFPLENBQUMsQ0FBQyxlQUFlLEVBQUV0QyxnQkFBZ0I7Z0JBQ2hGLElBQUlvQyxnQkFBZ0I7b0JBQ2xCLFNBQVM7b0JBQ1RHLGtCQUFrQkgsZ0JBQWdCSSxJQUFJOzhDQUFDOzRCQUNyQ0gsZUFBZUksVUFBVSxDQUFDLENBQUMsZUFBZSxFQUFFekMsZ0JBQWdCO3dCQUM5RDs7Z0JBQ0Y7WUFDRjtRQUNGOzZCQUFHO1FBQUNFO1FBQWNFLFNBQVMrQixNQUFNO1FBQUVuQztLQUFlO0lBRWxELE1BQU11QyxvQkFBb0IsT0FBT0c7UUFDL0IsSUFBSSxDQUFDQSxRQUFRQyxJQUFJLE1BQU0sQ0FBQ3pDLGNBQWM7UUFFdEMsWUFBWTtRQUNaLE1BQU0wQyxjQUF1QjtZQUMzQjNDLElBQUk2QixLQUFLZSxHQUFHLEdBQUdDLFFBQVE7WUFDdkIzQixTQUFTdUI7WUFDVHJCLE1BQU07WUFDTkMsV0FBVyxJQUFJUTtZQUNmOUIsZ0JBQWdCRSxhQUFhRCxFQUFFO1FBQ2pDO1FBQ0FJLFlBQVkwQyxDQUFBQSxPQUFRO21CQUFJQTtnQkFBTUg7YUFBWTtRQUUxQyxvQkFBb0I7UUFDcEIsTUFBTUksa0JBQWtCLFVBQVVsQixLQUFLZSxHQUFHO1FBQzFDLE1BQU1JLG1CQUE0QjtZQUNoQ2hELElBQUkrQztZQUNKN0IsU0FBUztZQUNURSxNQUFNO1lBQ05DLFdBQVcsSUFBSVE7WUFDZjlCLGdCQUFnQkUsYUFBYUQsRUFBRTtRQUNqQztRQUNBSSxZQUFZMEMsQ0FBQUEsT0FBUTttQkFBSUE7Z0JBQU1FO2FBQWlCO1FBRS9DLElBQUk7WUFDRixNQUFNdkQsNkNBQU9BLENBQUN3RCxpQkFBaUIsQ0FBQ2xELGdCQUFnQjtnQkFDOUMwQyxTQUFTQTtZQUNYLEdBQUcsQ0FBQ1M7Z0JBQ0YsT0FBUUEsTUFBTUMsSUFBSTtvQkFDaEIsS0FBSzt3QkFFSDtvQkFFRixLQUFLO3dCQUNILDhCQUE4Qjt3QkFDOUIsSUFBSUQsTUFBTWhDLE9BQU8sRUFBRTs0QkFDakJkLFlBQVkwQyxDQUFBQSxPQUFRQSxLQUFLTSxHQUFHLENBQUNDLENBQUFBLE1BQzNCQSxJQUFJckQsRUFBRSxLQUFLK0Msa0JBQ1A7d0NBQUUsR0FBR00sR0FBRzt3Q0FBRW5DLFNBQVNnQyxNQUFNaEMsT0FBTyxJQUFJO29DQUFHLElBQ3ZDbUM7d0JBRVI7d0JBQ0E7b0JBRUYsS0FBSzt3QkFDSCxrQkFBa0I7d0JBQ2xCakQsWUFBWTBDLENBQUFBLE9BQVFBLEtBQUtNLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFDM0JBLElBQUlyRCxFQUFFLEtBQUsrQyxrQkFDUDtvQ0FDQSxHQUFHTSxHQUFHO29DQUNOckQsSUFBSWtELE1BQU1JLFVBQVUsSUFBSVA7b0NBQ3hCdkIsVUFBVTBCLE1BQU0xQixRQUFRO2dDQUMxQixJQUNFNkI7d0JBRU47b0JBRUYsS0FBSzt3QkFDSCxPQUFPO3dCQUNQcEIsUUFBUUQsS0FBSyxDQUFDLFdBQVdrQixNQUFNbEIsS0FBSzt3QkFDcEM1QixZQUFZMEMsQ0FBQUEsT0FBUUEsS0FBS00sR0FBRyxDQUFDQyxDQUFBQSxNQUMzQkEsSUFBSXJELEVBQUUsS0FBSytDLGtCQUNQO29DQUFFLEdBQUdNLEdBQUc7b0NBQUVuQyxTQUFTLGNBQWNnQyxNQUFNbEIsS0FBSztnQ0FBQyxJQUM3Q3FCO3dCQUVOO2dCQUNKO1lBQ0Y7UUFDRixFQUFFLE9BQU9yQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxXQUFXQTtZQUN6QixTQUFTO1lBQ1Q1QixZQUFZMEMsQ0FBQUEsT0FBUUEsS0FBS00sR0FBRyxDQUFDQyxDQUFBQSxNQUMzQkEsSUFBSXJELEVBQUUsS0FBSytDLGtCQUNQO3dCQUFFLEdBQUdNLEdBQUc7d0JBQUVuQyxTQUFTO29CQUFpQixJQUNwQ21DO1FBRVI7SUFDRjtJQUlBLElBQUloRCxTQUFTO1FBQ1gscUJBQ0UsOERBQUNrRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNEO3dCQUFJQyxXQUFVO3dCQUFrREMsT0FBTzs0QkFBRUMsZ0JBQWdCO3dCQUFPOzs7Ozs7a0NBQ2pHLDhEQUFDSDt3QkFBSUMsV0FBVTt3QkFBa0RDLE9BQU87NEJBQUVDLGdCQUFnQjt3QkFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJekc7SUFFQSxJQUFJLENBQUN6RCxjQUFjO1FBQ2pCLHFCQUNFLDhEQUFDc0Q7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRzt3QkFBR0gsV0FBVTtrQ0FBMkM7Ozs7OztrQ0FDekQsOERBQUNJO3dCQUFFSixXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJckM7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDOUQsdUVBQVlBO29CQUFDUyxVQUFVQTs7Ozs7Ozs7Ozs7MEJBSTFCLDhEQUFDb0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDN0QsaUVBQVNBO3dCQUFDa0UsUUFBUXZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzdCIiwic291cmNlcyI6WyJEOlxcZGF0YVxcc3R1ZHlcXHB5dGhvblxccmFnXFxyYWctZnJvbnRlbmRcXHNyY1xcYXBwXFxjaGF0XFxbaWRdXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyB1c2VQYXJhbXMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBjaGF0QXBpIH0gZnJvbSAnQC9saWIvYXBpJztcclxuaW1wb3J0IHsgQ29udmVyc2F0aW9uLCBNZXNzYWdlIH0gZnJvbSAnQC9saWIvdHlwZXMnO1xyXG5pbXBvcnQgeyBDaGF0TWVzc2FnZXMgfSBmcm9tICdAL2NvbXBvbmVudHMvY2hhdC9DaGF0TWVzc2FnZXMnO1xyXG5pbXBvcnQgeyBDaGF0SW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvY2hhdC9DaGF0SW5wdXQnO1xyXG5pbXBvcnQgeyB1c2VDaGF0U3RvcmUgfSBmcm9tICdAL2xpYi9zdG9yZSc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDaGF0UGFnZSgpIHtcclxuICBjb25zdCBwYXJhbXMgPSB1c2VQYXJhbXMoKTtcclxuICBjb25zdCBjb252ZXJzYXRpb25JZCA9IHBhcmFtcy5pZCBhcyBzdHJpbmc7XHJcblxyXG4gIGNvbnN0IFtjb252ZXJzYXRpb24sIHNldENvbnZlcnNhdGlvbl0gPSB1c2VTdGF0ZTxDb252ZXJzYXRpb24gfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbbWVzc2FnZXMsIHNldE1lc3NhZ2VzXSA9IHVzZVN0YXRlPE1lc3NhZ2VbXT4oW10pO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IHsgc2V0Q29udmVyc2F0aW9uVGl0bGUgfSA9IHVzZUNoYXRTdG9yZSgpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCFjb252ZXJzYXRpb25JZCkgcmV0dXJuO1xyXG5cclxuICAgIGNvbnN0IGxvYWRDb252ZXJzYXRpb25BbmRNZXNzYWdlcyA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG5cclxuICAgICAgICAvLyDpgJrov4fljoblj7LmjqXlj6PlkIzml7bojrflj5blr7nor53kv6Hmga/lkozmtojmga9cclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNoYXRBcGkuZ2V0Q29udmVyc2F0aW9uSGlzdG9yeShjb252ZXJzYXRpb25JZCk7XHJcblxyXG4gICAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGEpIHtcclxuICAgICAgICAgIHNldENvbnZlcnNhdGlvbihyZXNwb25zZS5kYXRhLmNvbnZlcnNhdGlvbik7XHJcbiAgICAgICAgICBzZXRDb252ZXJzYXRpb25UaXRsZShyZXNwb25zZS5kYXRhLmNvbnZlcnNhdGlvbi50aXRsZSk7XHJcblxyXG4gICAgICAgICAgLy8g5bCGcWFfcGFpcnPovazmjaLkuLpNZXNzYWdl5qC85byPXHJcbiAgICAgICAgICBjb25zdCBtZXNzYWdlczogTWVzc2FnZVtdID0gW107XHJcbiAgICAgICAgICByZXNwb25zZS5kYXRhLnFhX3BhaXJzLmZvckVhY2gocWEgPT4ge1xyXG4gICAgICAgICAgICAvLyDmt7vliqDnlKjmiLfmtojmga9cclxuICAgICAgICAgICAgbWVzc2FnZXMucHVzaCh7XHJcbiAgICAgICAgICAgICAgaWQ6IHFhLmlkICsgJ191c2VyJyxcclxuICAgICAgICAgICAgICBjb250ZW50OiBxYS5xdWVyeSxcclxuICAgICAgICAgICAgICByb2xlOiAndXNlcicsXHJcbiAgICAgICAgICAgICAgdGltZXN0YW1wOiBxYS5jcmVhdGVkX2F0LFxyXG4gICAgICAgICAgICAgIGNvbnZlcnNhdGlvbklkOiBjb252ZXJzYXRpb25JZFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgLy8g5re75Yqg5Yqp5omL5Zue5aSNXHJcbiAgICAgICAgICAgIG1lc3NhZ2VzLnB1c2goe1xyXG4gICAgICAgICAgICAgIGlkOiBxYS5pZCArICdfYXNzaXN0YW50JyxcclxuICAgICAgICAgICAgICBjb250ZW50OiBxYS5hbnN3ZXIsXHJcbiAgICAgICAgICAgICAgcm9sZTogJ2Fzc2lzdGFudCcsXHJcbiAgICAgICAgICAgICAgdGltZXN0YW1wOiBxYS5jcmVhdGVkX2F0LFxyXG4gICAgICAgICAgICAgIGNvbnZlcnNhdGlvbklkOiBjb252ZXJzYXRpb25JZCxcclxuICAgICAgICAgICAgICBtZXRhZGF0YTogcWEubWV0YWRhdGFcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAvLyDmjInml7bpl7TmjpLluo/mtojmga9cclxuICAgICAgICAgIG1lc3NhZ2VzLnNvcnQoKGEsIGIpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgdGltZUEgPSB0eXBlb2YgYS50aW1lc3RhbXAgPT09ICdzdHJpbmcnID8gbmV3IERhdGUoYS50aW1lc3RhbXApIDogYS50aW1lc3RhbXA7XHJcbiAgICAgICAgICAgIGNvbnN0IHRpbWVCID0gdHlwZW9mIGIudGltZXN0YW1wID09PSAnc3RyaW5nJyA/IG5ldyBEYXRlKGIudGltZXN0YW1wKSA6IGIudGltZXN0YW1wO1xyXG4gICAgICAgICAgICByZXR1cm4gdGltZUEuZ2V0VGltZSgpIC0gdGltZUIuZ2V0VGltZSgpO1xyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgc2V0TWVzc2FnZXMobWVzc2FnZXMpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3lr7nor53lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGxvYWRDb252ZXJzYXRpb25BbmRNZXNzYWdlcygpO1xyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIHNldENvbnZlcnNhdGlvblRpdGxlKG51bGwpO1xyXG4gICAgfTtcclxuICB9LCBbY29udmVyc2F0aW9uSWQsIHNldENvbnZlcnNhdGlvblRpdGxlXSk7XHJcblxyXG4gIC8vIOWkhOeQhuWIneWni+a2iOaBr++8iOS7jnNlc3Npb25TdG9yYWdl6I635Y+W77yJXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChjb252ZXJzYXRpb24gJiYgbWVzc2FnZXMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgIGNvbnN0IGluaXRpYWxNZXNzYWdlID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbShgaW5pdGlhbE1lc3NhZ2VfJHtjb252ZXJzYXRpb25JZH1gKTtcclxuICAgICAgaWYgKGluaXRpYWxNZXNzYWdlKSB7XHJcbiAgICAgICAgLy8g5Y+R6YCB5Yid5aeL5raI5oGvXHJcbiAgICAgICAgaGFuZGxlU2VuZE1lc3NhZ2UoaW5pdGlhbE1lc3NhZ2UpLnRoZW4oKCkgPT4ge1xyXG4gICAgICAgICAgc2Vzc2lvblN0b3JhZ2UucmVtb3ZlSXRlbShgaW5pdGlhbE1lc3NhZ2VfJHtjb252ZXJzYXRpb25JZH1gKTtcclxuICAgICAgICB9KTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH0sIFtjb252ZXJzYXRpb24sIG1lc3NhZ2VzLmxlbmd0aCwgY29udmVyc2F0aW9uSWRdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2VuZE1lc3NhZ2UgPSBhc3luYyAobWVzc2FnZTogc3RyaW5nKSA9PiB7XHJcbiAgICBpZiAoIW1lc3NhZ2UudHJpbSgpIHx8ICFjb252ZXJzYXRpb24pIHJldHVybjtcclxuXHJcbiAgICAvLyDmt7vliqDnlKjmiLfmtojmga/liLDnlYzpnaJcclxuICAgIGNvbnN0IHVzZXJNZXNzYWdlOiBNZXNzYWdlID0ge1xyXG4gICAgICBpZDogRGF0ZS5ub3coKS50b1N0cmluZygpLFxyXG4gICAgICBjb250ZW50OiBtZXNzYWdlLFxyXG4gICAgICByb2xlOiAndXNlcicsXHJcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcclxuICAgICAgY29udmVyc2F0aW9uSWQ6IGNvbnZlcnNhdGlvbi5pZFxyXG4gICAgfTtcclxuICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIHVzZXJNZXNzYWdlXSk7XHJcblxyXG4gICAgLy8g5Yib5bu65LiA5Liq5Li05pe255qE5Yqp5omL5raI5oGv55So5LqO5rWB5byP5pu05pawXHJcbiAgICBjb25zdCB0ZW1wQXNzaXN0YW50SWQgPSAndGVtcF8nICsgRGF0ZS5ub3coKTtcclxuICAgIGNvbnN0IGFzc2lzdGFudE1lc3NhZ2U6IE1lc3NhZ2UgPSB7XHJcbiAgICAgIGlkOiB0ZW1wQXNzaXN0YW50SWQsXHJcbiAgICAgIGNvbnRlbnQ6ICcnLFxyXG4gICAgICByb2xlOiAnYXNzaXN0YW50JyxcclxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxyXG4gICAgICBjb252ZXJzYXRpb25JZDogY29udmVyc2F0aW9uLmlkXHJcbiAgICB9O1xyXG4gICAgc2V0TWVzc2FnZXMocHJldiA9PiBbLi4ucHJldiwgYXNzaXN0YW50TWVzc2FnZV0pO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGF3YWl0IGNoYXRBcGkuc2VuZE1lc3NhZ2VTdHJlYW0oY29udmVyc2F0aW9uSWQsIHtcclxuICAgICAgICBtZXNzYWdlOiBtZXNzYWdlXHJcbiAgICAgIH0sIChjaHVuaykgPT4ge1xyXG4gICAgICAgIHN3aXRjaCAoY2h1bmsudHlwZSkge1xyXG4gICAgICAgICAgY2FzZSAnc3RhcnQnOlxyXG4gICAgICAgICAgICAvLyDmtYHlvI/lvIDlp4vvvIzlj6/ku6XmmL7npLrmiZPlrZfmjIfnpLrlmahcclxuICAgICAgICAgICAgYnJlYWs7XHJcblxyXG4gICAgICAgICAgY2FzZSAnY29udGVudCc6XHJcbiAgICAgICAgICAgIC8vIOabtOaWsOWKqeaJi+a2iOaBr+WGheWuue+8iOebtOaOpeabv+aNou+8jOWboOS4uuWQjuerr+i/lOWbnueahOaYr+WujOaVtOaWh+acrO+8iVxyXG4gICAgICAgICAgICBpZiAoY2h1bmsuY29udGVudCkge1xyXG4gICAgICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gcHJldi5tYXAobXNnID0+XHJcbiAgICAgICAgICAgICAgICBtc2cuaWQgPT09IHRlbXBBc3Npc3RhbnRJZFxyXG4gICAgICAgICAgICAgICAgICA/IHsgLi4ubXNnLCBjb250ZW50OiBjaHVuay5jb250ZW50IHx8ICcnIH1cclxuICAgICAgICAgICAgICAgICAgOiBtc2dcclxuICAgICAgICAgICAgICApKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBicmVhaztcclxuXHJcbiAgICAgICAgICBjYXNlICdkb25lJzpcclxuICAgICAgICAgICAgLy8g5rWB5byP5a6M5oiQ77yM5pu05paw5raI5oGvSUTlkozlhYPmlbDmja5cclxuICAgICAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2Lm1hcChtc2cgPT5cclxuICAgICAgICAgICAgICBtc2cuaWQgPT09IHRlbXBBc3Npc3RhbnRJZFxyXG4gICAgICAgICAgICAgICAgPyB7XHJcbiAgICAgICAgICAgICAgICAgIC4uLm1zZyxcclxuICAgICAgICAgICAgICAgICAgaWQ6IGNodW5rLnFhX3BhaXJfaWQgfHwgdGVtcEFzc2lzdGFudElkLFxyXG4gICAgICAgICAgICAgICAgICBtZXRhZGF0YTogY2h1bmsubWV0YWRhdGFcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIDogbXNnXHJcbiAgICAgICAgICAgICkpO1xyXG4gICAgICAgICAgICBicmVhaztcclxuXHJcbiAgICAgICAgICBjYXNlICdlcnJvcic6XHJcbiAgICAgICAgICAgIC8vIOWkhOeQhumUmeivr1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmtYHlvI/lk43lupTplJnor686JywgY2h1bmsuZXJyb3IpO1xyXG4gICAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYubWFwKG1zZyA9PlxyXG4gICAgICAgICAgICAgIG1zZy5pZCA9PT0gdGVtcEFzc2lzdGFudElkXHJcbiAgICAgICAgICAgICAgICA/IHsgLi4ubXNnLCBjb250ZW50OiAn5oqx5q2J77yM5Y+R55Sf5LqG6ZSZ6K+v77yaJyArIGNodW5rLmVycm9yIH1cclxuICAgICAgICAgICAgICAgIDogbXNnXHJcbiAgICAgICAgICAgICkpO1xyXG4gICAgICAgICAgICBicmVhaztcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign5Y+R6YCB5raI5oGv5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgLy8g5pu05paw6ZSZ6K+v5raI5oGvXHJcbiAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gcHJldi5tYXAobXNnID0+XHJcbiAgICAgICAgbXNnLmlkID09PSB0ZW1wQXNzaXN0YW50SWRcclxuICAgICAgICAgID8geyAuLi5tc2csIGNvbnRlbnQ6ICfmirHmrYnvvIzlj5HpgIHmtojmga/lpLHotKXvvIzor7fph43or5XjgIInIH1cclxuICAgICAgICAgIDogbXNnXHJcbiAgICAgICkpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG5cclxuXHJcbiAgaWYgKGxvYWRpbmcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMVwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWJsdWUtNTAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLWJvdW5jZVwiPjwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWJsdWUtNTAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLWJvdW5jZVwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnMC4xcycgfX0+PC9kaXY+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmx1ZS01MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcwLjJzJyB9fT48L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgaWYgKCFjb252ZXJzYXRpb24pIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj7lr7nor53kuI3lrZjlnKg8L2gyPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPuivt+ajgOafpemTvuaOpeaYr+WQpuato+ehrjwvcD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGZsZXggZmxleC1jb2wgYmctd2hpdGVcIj5cclxuICAgICAgey8qIOa2iOaBr+WIl+ihqCAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWF4LXctM3hsIG14LWF1dG9cIj5cclxuICAgICAgICA8Q2hhdE1lc3NhZ2VzIG1lc3NhZ2VzPXttZXNzYWdlc30gLz5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7Lyog6L6T5YWl5qGGICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0aWNreSBib3R0b20tMCBiZy13aGl0ZS85MCBiYWNrZHJvcC1ibHVyLXhsIGJvcmRlci10IGJvcmRlci1ncmF5LTEwMC81MCBweC02IHB5LTYgZmxleC1zaHJpbmstMFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctM3hsIG14LWF1dG9cIj5cclxuICAgICAgICAgIDxDaGF0SW5wdXQgb25TZW5kPXtoYW5kbGVTZW5kTWVzc2FnZX0gLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VQYXJhbXMiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsImNoYXRBcGkiLCJDaGF0TWVzc2FnZXMiLCJDaGF0SW5wdXQiLCJ1c2VDaGF0U3RvcmUiLCJDaGF0UGFnZSIsInBhcmFtcyIsImNvbnZlcnNhdGlvbklkIiwiaWQiLCJjb252ZXJzYXRpb24iLCJzZXRDb252ZXJzYXRpb24iLCJtZXNzYWdlcyIsInNldE1lc3NhZ2VzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJzZXRDb252ZXJzYXRpb25UaXRsZSIsImxvYWRDb252ZXJzYXRpb25BbmRNZXNzYWdlcyIsInJlc3BvbnNlIiwiZ2V0Q29udmVyc2F0aW9uSGlzdG9yeSIsInN1Y2Nlc3MiLCJkYXRhIiwidGl0bGUiLCJxYV9wYWlycyIsImZvckVhY2giLCJxYSIsInB1c2giLCJjb250ZW50IiwicXVlcnkiLCJyb2xlIiwidGltZXN0YW1wIiwiY3JlYXRlZF9hdCIsImFuc3dlciIsIm1ldGFkYXRhIiwic29ydCIsImEiLCJiIiwidGltZUEiLCJEYXRlIiwidGltZUIiLCJnZXRUaW1lIiwiZXJyb3IiLCJjb25zb2xlIiwibGVuZ3RoIiwiaW5pdGlhbE1lc3NhZ2UiLCJzZXNzaW9uU3RvcmFnZSIsImdldEl0ZW0iLCJoYW5kbGVTZW5kTWVzc2FnZSIsInRoZW4iLCJyZW1vdmVJdGVtIiwibWVzc2FnZSIsInRyaW0iLCJ1c2VyTWVzc2FnZSIsIm5vdyIsInRvU3RyaW5nIiwicHJldiIsInRlbXBBc3Npc3RhbnRJZCIsImFzc2lzdGFudE1lc3NhZ2UiLCJzZW5kTWVzc2FnZVN0cmVhbSIsImNodW5rIiwidHlwZSIsIm1hcCIsIm1zZyIsInFhX3BhaXJfaWQiLCJkaXYiLCJjbGFzc05hbWUiLCJzdHlsZSIsImFuaW1hdGlvbkRlbGF5IiwiaDIiLCJwIiwib25TZW5kIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/chat/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/chat/layout.tsx":
/*!*********************************!*\
  !*** ./src/app/chat/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_chat_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/Sidebar */ \"(ssr)/./src/components/chat/Sidebar.tsx\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction ChatLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { conversationTitle } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useChatStore)();\n    // 新建会话页面显示侧边栏但不显示折叠按钮\n    const isNewChatPage = pathname === '/chat/new';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${sidebarOpen ? 'w-68' : 'w-0'} transition-all duration-300 ease-in-out overflow-hidden`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_Sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    !isNewChatPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center px-4 py-3 border-b border-gray-200 bg-white/80 backdrop-blur-sm z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                className: \"p-2 cursor-pointer hover:bg-gray-100 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 18\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\layout.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\layout.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            conversationTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"ml-4 text-lg font-semibold text-gray-800\",\n                                children: conversationTitle\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\layout.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 35\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\app\\\\chat\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/chat/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/ChatInput.tsx":
/*!*******************************************!*\
  !*** ./src/components/chat/ChatInput.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInput: () => (/* binding */ ChatInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Paperclip,Send!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Paperclip,Send!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInput auto */ \n\n\nfunction ChatInput({ onSend, disabled = false, placeholder = \"输入消息...\" }) {\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleSend = ()=>{\n        if (message.trim() && !disabled) {\n            onSend(message.trim());\n            setMessage('');\n            // 重置textarea高度\n            if (textareaRef.current) {\n                textareaRef.current.style.height = 'auto';\n            }\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleInputChange = (e)=>{\n        setMessage(e.target.value);\n        // 自动调整textarea高度\n        const textarea = e.target;\n        textarea.style.height = 'auto';\n        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative bg-white border border-gray-200 rounded-2xl transition-all duration-200 focus-within:border-gray-300\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 pt-4 pb-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        ref: textareaRef,\n                        value: message,\n                        onChange: handleInputChange,\n                        onKeyDown: handleKeyDown,\n                        placeholder: placeholder,\n                        disabled: disabled,\n                        className: \"w-full resize-none bg-transparent border-0 outline-none text-gray-900 placeholder-gray-400 text-base leading-6 min-h-[60px] max-h-[200px] font-normal\",\n                        rows: 3,\n                        style: {\n                            height: 'auto'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between px-4 pb-4 pt-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                disabled: disabled,\n                                className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    size: 18\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: handleSend,\n                            disabled: !message.trim() || disabled,\n                            className: `px-4 py-2 rounded-lg font-medium transition-colors ${message.trim() && !disabled ? 'bg-orange-100 text-orange-600 hover:bg-orange-200' : 'bg-gray-100 text-gray-400 cursor-not-allowed'}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"发送\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/ChatInput.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/ChatMessages.tsx":
/*!**********************************************!*\
  !*** ./src/components/chat/ChatMessages.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatMessages: () => (/* binding */ ChatMessages)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MessageItem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageItem */ \"(ssr)/./src/components/chat/MessageItem.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatMessages auto */ \n\n\nfunction ChatMessages({ messages }) {\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatMessages.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatMessages.useEffect\"], [\n        messages\n    ]);\n    if (messages.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"mx-auto text-gray-300 mb-4\",\n                        width: \"48\",\n                        height: \"48\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"1.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        viewBox: \"0 0 24 24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatMessages.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M8 14s1.5 2 4 2 4-2 4-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatMessages.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"9\",\n                                cy: \"10\",\n                                r: \"1\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatMessages.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"15\",\n                                cy: \"10\",\n                                r: \"1\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatMessages.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatMessages.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"开始对话吧！\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatMessages.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatMessages.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatMessages.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-6 py-4 space-y-6\",\n        children: [\n            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageItem__WEBPACK_IMPORTED_MODULE_2__.MessageItem, {\n                    message: message\n                }, message.id, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatMessages.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: messagesEndRef\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatMessages.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatMessages.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/ChatMessages.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/MessageItem.tsx":
/*!*********************************************!*\
  !*** ./src/components/chat/MessageItem.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageItem: () => (/* binding */ MessageItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/.pnpm/react-markdown@10.1.0_@types+react@19.1.9_react@19.1.0/node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Bot_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* __next_internal_client_entry_do_not_use__ MessageItem auto */ \n\n\n\n\nfunction MessageItem({ message }) {\n    const isUser = message.role === 'user';\n    const isStreaming = message.id.startsWith('temp_') && !isUser;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex gap-4 ${isUser ? 'flex-row-reverse' : ''}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${isUser ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n                children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\MessageItem.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 19\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\MessageItem.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 40\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\MessageItem.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex-1 max-w-3xl ${isUser ? 'text-right' : ''}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `inline-block p-4 rounded-2xl ${isUser ? 'bg-blue-500 text-white rounded-br-md' : 'bg-gray-100 text-gray-900 rounded-bl-md'}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"whitespace-pre-wrap break-words markdown-body\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_5__.Markdown, {\n                                    children: message.content\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\MessageItem.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block w-2 h-5 bg-gray-400 ml-1 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\MessageItem.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\MessageItem.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\MessageItem.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-xs text-gray-400 mt-2 ${isUser ? 'text-right' : ''}`,\n                        children: [\n                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatTime)(typeof message.timestamp === 'string' ? message.timestamp : message.timestamp.toISOString()),\n                            message.metadata?.responseTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2\",\n                                children: [\n                                    \"(\",\n                                    message.metadata.responseTime,\n                                    \"ms)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\MessageItem.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\MessageItem.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\MessageItem.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\MessageItem.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/MessageItem.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/Sidebar.tsx":
/*!*****************************************!*\
  !*** ./src/components/chat/Sidebar.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Copy,Download,Edit3,MessageSquare,MoreHorizontal,Pin,Plus,Search,Sparkles,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Copy,Download,Edit3,MessageSquare,MoreHorizontal,Pin,Plus,Search,Sparkles,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Copy,Download,Edit3,MessageSquare,MoreHorizontal,Pin,Plus,Search,Sparkles,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Copy,Download,Edit3,MessageSquare,MoreHorizontal,Pin,Plus,Search,Sparkles,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Copy,Download,Edit3,MessageSquare,MoreHorizontal,Pin,Plus,Search,Sparkles,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Copy,Download,Edit3,MessageSquare,MoreHorizontal,Pin,Plus,Search,Sparkles,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Copy,Download,Edit3,MessageSquare,MoreHorizontal,Pin,Plus,Search,Sparkles,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Copy,Download,Edit3,MessageSquare,MoreHorizontal,Pin,Plus,Search,Sparkles,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Copy,Download,Edit3,MessageSquare,MoreHorizontal,Pin,Plus,Search,Sparkles,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Copy,Download,Edit3,MessageSquare,MoreHorizontal,Pin,Plus,Search,Sparkles,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Copy,Download,Edit3,MessageSquare,MoreHorizontal,Pin,Plus,Search,Sparkles,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Copy,Download,Edit3,MessageSquare,MoreHorizontal,Pin,Plus,Search,Sparkles,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_debounce_lodash_es__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=debounce!=!lodash-es */ \"(ssr)/./node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/debounce.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\n\n\nfunction Sidebar({ onToggle }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 从URL路径中提取当前活跃的对话ID\n    const activeConversationId = pathname.startsWith('/chat/') && pathname !== '/chat/new' ? pathname.split('/')[2] : null;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            loadConversations();\n        }\n    }[\"Sidebar.useEffect\"], []);\n    const loadConversations = async ()=>{\n        setLoading(true);\n        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.chatApi.getConversations();\n        if (response.success && response.data) {\n            setConversations(response.data);\n        }\n        setLoading(false);\n    };\n    const debouncedSearch = (0,_barrel_optimize_names_debounce_lodash_es__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((query)=>{\n        setSearchQuery(query);\n    }, 300);\n    const createNewConversation = ()=>{\n        // 跳转到新建对话页面\n        router.push('/chat/new');\n    };\n    const selectConversation = (conversationId)=>{\n        // 跳转到具体对话页面\n        router.push(`/chat/${conversationId}`);\n    };\n    const deleteConversation = async (id, e)=>{\n        e.stopPropagation();\n        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.chatApi.deleteConversation(id);\n        if (response.success) {\n            await loadConversations();\n            if (activeConversationId === id) {\n                router.push('/chat/new');\n            }\n        }\n    };\n    const filteredConversations = conversations.filter((conv)=>conv.title.toLowerCase().includes(searchQuery.toLowerCase()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-white/80 backdrop-blur-xl border-r border-slate-200/60 flex flex-col shadow-xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-slate-200/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 18,\n                                    className: \"text-white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"font-semibold text-slate-900\",\n                                        children: \"AI 助手\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-slate-500\",\n                                        children: \"智能对话系统\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: createNewConversation,\n                        className: \"w-full cursor-pointer bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 h-11\",\n                        size: \"lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: 18,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            \"新建对话\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400\",\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: \"搜索对话...\",\n                                className: \"pl-9 bg-slate-50/80 border-slate-200/60 focus:bg-white focus:border-blue-300 transition-all duration-200\",\n                                onChange: (e)=>debouncedSearch(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 flex-wrap\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"h-7 px-2 text-xs bg-slate-100/80 hover:bg-slate-200/80 text-slate-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        size: 12,\n                                        className: \"mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"置顶\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"h-7 px-2 text-xs bg-slate-100/80 hover:bg-slate-200/80 text-slate-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        size: 12,\n                                        className: \"mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"收藏\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"h-7 px-2 text-xs bg-slate-100/80 hover:bg-slate-200/80 text-slate-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 12,\n                                        className: \"mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"归档\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto px-2\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: '0.1s'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: '0.2s'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this) : filteredConversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-slate-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: 32,\n                            className: \"mx-auto mb-2 opacity-50\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: searchQuery ? '没有找到匹配的对话' : '还没有对话记录'\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: filteredConversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConversationItem, {\n                            conversation: conversation,\n                            isActive: activeConversationId === conversation.id,\n                            onSelect: ()=>selectConversation(conversation.id),\n                            onDelete: (e)=>deleteConversation(conversation.id, e)\n                        }, conversation.id, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-slate-200/60 bg-slate-50/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-9 h-9 bg-gradient-to-r from-emerald-400 to-cyan-400 rounded-full flex items-center justify-center text-white text-sm font-medium shadow-lg\",\n                            children: \"U\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-medium text-slate-900 truncate\",\n                                    children: \"用户\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-slate-500 truncate\",\n                                    children: \"<EMAIL>\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            className: \"text-slate-400 hover:text-slate-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\nfunction ConversationItem({ conversation, isActive, onSelect, onDelete }) {\n    const [showActions, setShowActions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"group relative p-3 rounded-xl cursor-pointer transition-all duration-200 mx-1 border\", isActive ? \"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200/60 shadow-sm\" : \"hover:bg-slate-50/80 border-transparent\"),\n        onClick: onSelect,\n        onMouseEnter: ()=>setShowActions(true),\n        onMouseLeave: ()=>setShowActions(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-1 mt-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-2 h-2 rounded-full flex-shrink-0\", isActive ? \"bg-blue-500\" : \"bg-slate-300\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            conversation.pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 8,\n                                className: \"text-amber-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            conversation.starred && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 8,\n                                className: \"text-yellow-500 fill-current\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-sm font-medium truncate\", isActive ? \"text-slate-900\" : \"text-slate-700\"),\n                                children: conversation.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            conversation.last_message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-slate-500 truncate mt-1\",\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.truncateText)(conversation.last_message, 45)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-slate-400 mt-1\",\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatTime)(conversation.updated_at)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConversationActions, {\n                onDelete: onDelete\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                lineNumber: 244,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, this);\n}\nfunction ConversationActions({ onDelete }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const closeMenu = ()=>{\n        setIsOpen(false);\n    };\n    const menuItems = [\n        {\n            icon: _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            label: '重命名',\n            action: ()=>console.log('重命名')\n        },\n        {\n            icon: _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            label: '置顶对话',\n            action: ()=>console.log('置顶')\n        },\n        {\n            icon: _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            label: '收藏对话',\n            action: ()=>console.log('收藏')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            label: '复制链接',\n            action: ()=>console.log('复制链接')\n        },\n        {\n            icon: _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            label: '导出对话',\n            action: ()=>console.log('导出')\n        },\n        {\n            type: 'separator'\n        },\n        {\n            icon: _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            label: '归档对话',\n            action: ()=>console.log('归档')\n        },\n        {\n            icon: _barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            label: '删除对话',\n            action: (e)=>onDelete(e),\n            danger: true\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    className: \"h-6 w-6 p-0 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-md transition-colors duration-200\",\n                    onClick: (e)=>{\n                        e.stopPropagation();\n                        setIsOpen(!isOpen);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Copy_Download_Edit3_MessageSquare_MoreHorizontal_Pin_Plus_Search_Sparkles_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        size: 12\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 z-10\",\n                            onClick: closeMenu\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute right-0 top-full mt-1 w-48 bg-white/95 backdrop-blur-sm rounded-xl shadow-xl border border-slate-200/60 py-2 z-20 animate-in fade-in-0 zoom-in-95 duration-200 origin-top-right\",\n                            children: menuItems.map((item, index)=>{\n                                if (item.type === 'separator') {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-slate-200/60 my-1\"\n                                    }, index, false, {\n                                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 26\n                                    }, this);\n                                }\n                                const Icon = item.icon;\n                                if (!Icon) return null;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `w-full px-4 py-2.5 text-left text-sm flex items-center transition-all duration-150 rounded-lg mx-1 ${item.danger ? 'hover:bg-red-50 text-red-600 hover:text-red-700' : 'hover:bg-slate-50 text-slate-700 hover:text-slate-900'}`,\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeMenu();\n                                        item.action(e);\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            size: 14,\n                                            className: `mr-3 ${item.danger ? '' : 'text-slate-500'}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 21\n                                        }, this),\n                                        item.label\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n            lineNumber: 295,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\Sidebar.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.9_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Input({ className, type, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        \"data-slot\": \"input\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\", \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLFNBQVNFLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxJQUFJLEVBQUUsR0FBR0MsT0FBc0M7SUFDekUscUJBQ0UsOERBQUNDO1FBQ0NGLE1BQU1BO1FBQ05HLGFBQVU7UUFDVkosV0FBV0YsOENBQUVBLENBQ1gsbWNBQ0EsaUZBQ0EsMEdBQ0FFO1FBRUQsR0FBR0UsS0FBSzs7Ozs7O0FBR2Y7QUFFZ0IiLCJzb3VyY2VzIjpbIkQ6XFxkYXRhXFxzdHVkeVxccHl0aG9uXFxyYWdcXHJhZy1mcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcaW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZnVuY3Rpb24gSW5wdXQoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH06IFJlYWN0LkNvbXBvbmVudFByb3BzPFwiaW5wdXRcIj4pIHtcbiAgcmV0dXJuIChcbiAgICA8aW5wdXRcbiAgICAgIHR5cGU9e3R5cGV9XG4gICAgICBkYXRhLXNsb3Q9XCJpbnB1dFwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBzZWxlY3Rpb246YmctcHJpbWFyeSBzZWxlY3Rpb246dGV4dC1wcmltYXJ5LWZvcmVncm91bmQgZGFyazpiZy1pbnB1dC8zMCBib3JkZXItaW5wdXQgZmxleCBoLTkgdy1mdWxsIG1pbi13LTAgcm91bmRlZC1tZCBib3JkZXIgYmctdHJhbnNwYXJlbnQgcHgtMyBweS0xIHRleHQtYmFzZSBzaGFkb3cteHMgdHJhbnNpdGlvbi1bY29sb3IsYm94LXNoYWRvd10gb3V0bGluZS1ub25lIGZpbGU6aW5saW5lLWZsZXggZmlsZTpoLTcgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxuICAgICAgICBcImZvY3VzLXZpc2libGU6Ym9yZGVyLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcvNTAgZm9jdXMtdmlzaWJsZTpyaW5nLVszcHhdXCIsXG4gICAgICAgIFwiYXJpYS1pbnZhbGlkOnJpbmctZGVzdHJ1Y3RpdmUvMjAgZGFyazphcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS80MCBhcmlhLWludmFsaWQ6Ym9yZGVyLWRlc3RydWN0aXZlXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJpbnB1dCIsImRhdGEtc2xvdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chatApi: () => (/* binding */ chatApi)\n/* harmony export */ });\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\n// 通用API调用函数\nasync function apiCall(endpoint, options = {}) {\n    try {\n        const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n            headers: {\n                'Content-Type': 'application/json',\n                ...options.headers\n            },\n            ...options\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            return {\n                success: false,\n                error: data.detail || `HTTP ${response.status}`\n            };\n        }\n        return {\n            success: true,\n            data\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : '网络错误'\n        };\n    }\n}\n// 对话相关API\nconst chatApi = {\n    // 创建对话\n    createConversation: async (request)=>{\n        return apiCall('/chat/conversations', {\n            method: 'POST',\n            body: JSON.stringify(request)\n        });\n    },\n    // 获取对话列表\n    getConversations: async (limit = 50, offset = 0)=>{\n        return apiCall(`/chat/conversations?limit=${limit}&offset=${offset}`);\n    },\n    // 获取单个对话（通过历史接口获取）\n    getConversation: async (conversationId)=>{\n        const response = await apiCall(`/chat/conversations/${conversationId}/history?limit=1&offset=0`);\n        if (response.success && response.data) {\n            return {\n                success: true,\n                data: response.data.conversation\n            };\n        }\n        return {\n            success: false,\n            error: response.error || '获取对话失败'\n        };\n    },\n    // 获取对话消息（通过历史接口获取）\n    getMessages: async (conversationId, limit = 50, offset = 0)=>{\n        const response = await apiCall(`/chat/conversations/${conversationId}/history?limit=${limit}&offset=${offset}`);\n        if (response.success && response.data) {\n            // 将qa_pairs转换为Message格式\n            const messages = [];\n            response.data.qa_pairs.forEach((qa)=>{\n                // 添加用户消息\n                messages.push({\n                    id: qa.id + '_user',\n                    content: qa.query,\n                    role: 'user',\n                    timestamp: qa.created_at,\n                    conversationId: conversationId\n                });\n                // 添加助手回复\n                messages.push({\n                    id: qa.id + '_assistant',\n                    content: qa.answer,\n                    role: 'assistant',\n                    timestamp: qa.created_at,\n                    conversationId: conversationId,\n                    metadata: qa.metadata\n                });\n            });\n            // 按时间排序消息\n            messages.sort((a, b)=>{\n                const timeA = typeof a.timestamp === 'string' ? new Date(a.timestamp) : a.timestamp;\n                const timeB = typeof b.timestamp === 'string' ? new Date(b.timestamp) : b.timestamp;\n                return timeA.getTime() - timeB.getTime();\n            });\n            return {\n                success: true,\n                data: messages\n            };\n        }\n        return {\n            success: false,\n            error: response.error || '获取消息失败'\n        };\n    },\n    // 获取对话历史\n    getConversationHistory: async (conversationId, limit = 50, offset = 0)=>{\n        return apiCall(`/chat/conversations/${conversationId}/history?limit=${limit}&offset=${offset}`);\n    },\n    // 发送消息\n    sendMessage: async (conversationId, request)=>{\n        return apiCall(`/chat/conversations/${conversationId}/messages`, {\n            method: 'POST',\n            body: JSON.stringify(request)\n        });\n    },\n    // 发送消息（流式）\n    sendMessageStream: async (conversationId, request, onChunk)=>{\n        try {\n            const response = await fetch(`${API_BASE_URL}/chat/conversations/${conversationId}/messages/stream`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(request)\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP ${response.status}`);\n            }\n            const reader = response.body?.getReader();\n            if (!reader) {\n                throw new Error('无法获取响应流');\n            }\n            const decoder = new TextDecoder();\n            let buffer = '';\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) break;\n                buffer += decoder.decode(value, {\n                    stream: true\n                });\n                // 处理SSE数据\n                const lines = buffer.split('\\n');\n                buffer = lines.pop() || ''; // 保留不完整的行\n                for (const line of lines){\n                    if (line.startsWith('data: ')) {\n                        const data = line.slice(6);\n                        if (data === '[DONE]') {\n                            return;\n                        }\n                        try {\n                            const chunk = JSON.parse(data);\n                            onChunk(chunk);\n                        } catch (e) {\n                            console.error('解析SSE数据失败:', e, data);\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            onChunk({\n                type: 'error',\n                error: error instanceof Error ? error.message : '网络错误'\n            });\n        }\n    },\n    // 删除对话\n    deleteConversation: async (conversationId)=>{\n        return apiCall(`/chat/conversations/${conversationId}`, {\n            method: 'DELETE'\n        });\n    },\n    // 更新对话标题\n    updateConversation: async (conversationId, updates)=>{\n        return apiCall(`/chat/conversations/${conversationId}`, {\n            method: 'PUT',\n            body: JSON.stringify(updates)\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/store.ts":
/*!**************************!*\
  !*** ./src/lib/store.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useChatStore: () => (/* binding */ useChatStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/.pnpm/zustand@5.0.7_@types+react@19.1.9_react@19.1.0/node_modules/zustand/esm/react.mjs\");\n\nconst useChatStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        pendingMessage: null,\n        conversationTitle: null,\n        setPendingMessage: (message)=>{\n            set({\n                pendingMessage: message\n            });\n        },\n        clearPendingMessage: (conversationId)=>{\n            const current = get().pendingMessage;\n            if (current && current.conversationId === conversationId) {\n                set({\n                    pendingMessage: null\n                });\n            }\n        },\n        setConversationTitle: (title)=>{\n            set({\n                conversationTitle: title\n            });\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N0b3JlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlDO0FBZ0IxQixNQUFNQyxlQUFlRCwrQ0FBTUEsQ0FBWSxDQUFDRSxLQUFLQyxNQUFTO1FBQzNEQyxnQkFBZ0I7UUFDaEJDLG1CQUFtQjtRQUVuQkMsbUJBQW1CLENBQUNDO1lBQ2xCTCxJQUFJO2dCQUFFRSxnQkFBZ0JHO1lBQVE7UUFDaEM7UUFFQUMscUJBQXFCLENBQUNDO1lBQ3BCLE1BQU1DLFVBQVVQLE1BQU1DLGNBQWM7WUFDcEMsSUFBSU0sV0FBV0EsUUFBUUQsY0FBYyxLQUFLQSxnQkFBZ0I7Z0JBQ3hEUCxJQUFJO29CQUFFRSxnQkFBZ0I7Z0JBQUs7WUFDN0I7UUFDRjtRQUVBTyxzQkFBc0IsQ0FBQ0M7WUFDckJWLElBQUk7Z0JBQUVHLG1CQUFtQk87WUFBTTtRQUNqQztJQUNGLElBQUkiLCJzb3VyY2VzIjpbIkQ6XFxkYXRhXFxzdHVkeVxccHl0aG9uXFxyYWdcXHJhZy1mcm9udGVuZFxcc3JjXFxsaWJcXHN0b3JlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZSB9IGZyb20gJ3p1c3RhbmQnO1xyXG5cclxuaW50ZXJmYWNlIFBlbmRpbmdNZXNzYWdlIHtcclxuICBjb252ZXJzYXRpb25JZDogc3RyaW5nO1xyXG4gIG1lc3NhZ2U6IHN0cmluZztcclxuICB0aW1lc3RhbXA6IG51bWJlcjtcclxufVxyXG5cclxuaW50ZXJmYWNlIENoYXRTdG9yZSB7XHJcbiAgcGVuZGluZ01lc3NhZ2U6IFBlbmRpbmdNZXNzYWdlIHwgbnVsbDtcclxuICBzZXRQZW5kaW5nTWVzc2FnZTogKG1lc3NhZ2U6IFBlbmRpbmdNZXNzYWdlKSA9PiB2b2lkO1xyXG4gIGNsZWFyUGVuZGluZ01lc3NhZ2U6IChjb252ZXJzYXRpb25JZDogc3RyaW5nKSA9PiB2b2lkO1xyXG4gIGNvbnZlcnNhdGlvblRpdGxlOiBzdHJpbmcgfCBudWxsO1xyXG4gIHNldENvbnZlcnNhdGlvblRpdGxlOiAodGl0bGU6IHN0cmluZyB8IG51bGwpID0+IHZvaWQ7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCB1c2VDaGF0U3RvcmUgPSBjcmVhdGU8Q2hhdFN0b3JlPigoc2V0LCBnZXQpID0+ICh7XHJcbiAgcGVuZGluZ01lc3NhZ2U6IG51bGwsXHJcbiAgY29udmVyc2F0aW9uVGl0bGU6IG51bGwsXHJcbiAgXHJcbiAgc2V0UGVuZGluZ01lc3NhZ2U6IChtZXNzYWdlOiBQZW5kaW5nTWVzc2FnZSkgPT4ge1xyXG4gICAgc2V0KHsgcGVuZGluZ01lc3NhZ2U6IG1lc3NhZ2UgfSk7XHJcbiAgfSxcclxuICBcclxuICBjbGVhclBlbmRpbmdNZXNzYWdlOiAoY29udmVyc2F0aW9uSWQ6IHN0cmluZykgPT4ge1xyXG4gICAgY29uc3QgY3VycmVudCA9IGdldCgpLnBlbmRpbmdNZXNzYWdlO1xyXG4gICAgaWYgKGN1cnJlbnQgJiYgY3VycmVudC5jb252ZXJzYXRpb25JZCA9PT0gY29udmVyc2F0aW9uSWQpIHtcclxuICAgICAgc2V0KHsgcGVuZGluZ01lc3NhZ2U6IG51bGwgfSk7XHJcbiAgICB9XHJcbiAgfSxcclxuXHJcbiAgc2V0Q29udmVyc2F0aW9uVGl0bGU6ICh0aXRsZTogc3RyaW5nIHwgbnVsbCkgPT4ge1xyXG4gICAgc2V0KHsgY29udmVyc2F0aW9uVGl0bGU6IHRpdGxlIH0pO1xyXG4gIH0sXHJcbn0pKTsiXSwibmFtZXMiOlsiY3JlYXRlIiwidXNlQ2hhdFN0b3JlIiwic2V0IiwiZ2V0IiwicGVuZGluZ01lc3NhZ2UiLCJjb252ZXJzYXRpb25UaXRsZSIsInNldFBlbmRpbmdNZXNzYWdlIiwibWVzc2FnZSIsImNsZWFyUGVuZGluZ01lc3NhZ2UiLCJjb252ZXJzYXRpb25JZCIsImN1cnJlbnQiLCJzZXRDb252ZXJzYXRpb25UaXRsZSIsInRpdGxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/store.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* reexport safe */ _barrel_optimize_names_debounce_throttle_lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   throttle: () => (/* reexport safe */ _barrel_optimize_names_debounce_throttle_lodash_es__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* harmony import */ var _barrel_optimize_names_debounce_throttle_lodash_es__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=debounce,throttle!=!lodash-es */ \"(ssr)/./node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/debounce.js\");\n/* harmony import */ var _barrel_optimize_names_debounce_throttle_lodash_es__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=debounce,throttle!=!lodash-es */ \"(ssr)/./node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/throttle.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(ssr)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/formatDistanceToNow.js\");\n\n\n\n\n// Tailwind CSS类名合并\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// 格式化时间 - 使用date-fns\nfunction formatTime(date) {\n    try {\n        const d = typeof date === 'string' ? new Date(date) : date;\n        // 检查日期是否有效\n        if (isNaN(d.getTime())) {\n            return '时间格式错误';\n        }\n        return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_2__.formatDistanceToNow)(d, {\n            addSuffix: true\n        });\n    } catch (error) {\n        console.error('格式化时间出错:', error, '原始值:', date);\n        return '时间格式错误';\n    }\n}\n// 生成唯一ID - 可以考虑使用nanoid\nfunction generateId() {\n    return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n// 截断文本\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n}\n// 格式化文件大小\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = [\n        'B',\n        'KB',\n        'MB',\n        'GB',\n        'TB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n// 导出lodash的防抖和节流\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?96cf":
/*!********************************!*\
  !*** supports-color (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/tailwind-merge@3.3.1","vendor-chunks/date-fns@4.1.0","vendor-chunks/lucide-react@0.536.0_react@19.1.0","vendor-chunks/lodash-es@4.17.21","vendor-chunks/@radix-ui+react-slot@1.2.3_@types+react@19.1.9_react@19.1.0","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/class-variance-authority@0.7.1","vendor-chunks/zustand@5.0.7_@types+react@19.1.9_react@19.1.0","vendor-chunks/@radix-ui+react-compose-ref_1787abaede7e318501d42c829126c2d0","vendor-chunks/clsx@2.1.1","vendor-chunks/mdast-util-to-hast@13.2.0","vendor-chunks/micromark-core-commonmark@2.0.3","vendor-chunks/property-information@7.1.0","vendor-chunks/micromark@4.0.2","vendor-chunks/micromark-util-symbol@2.0.1","vendor-chunks/@ungap+structured-clone@1.3.0","vendor-chunks/debug@4.4.1","vendor-chunks/vfile@6.0.3","vendor-chunks/unist-util-visit-parents@6.0.1","vendor-chunks/unified@11.0.5","vendor-chunks/micromark-util-subtokenize@2.1.0","vendor-chunks/style-to-js@1.1.17","vendor-chunks/vfile-message@4.0.3","vendor-chunks/unist-util-visit@5.0.0","vendor-chunks/unist-util-stringify-position@4.0.0","vendor-chunks/unist-util-position@5.0.0","vendor-chunks/unist-util-is@6.0.0","vendor-chunks/trough@2.2.0","vendor-chunks/trim-lines@3.0.1","vendor-chunks/space-separated-tokens@2.0.2","vendor-chunks/remark-rehype@11.1.2","vendor-chunks/remark-parse@11.0.0","vendor-chunks/react-markdown@10.1.0_@types+react@19.1.9_react@19.1.0","vendor-chunks/micromark-util-sanitize-uri@2.0.1","vendor-chunks/micromark-util-resolve-all@2.0.1","vendor-chunks/micromark-util-normalize-identifier@2.0.1","vendor-chunks/micromark-util-html-tag-name@2.0.1","vendor-chunks/micromark-util-encode@2.0.1","vendor-chunks/micromark-util-decode-string@2.0.1","vendor-chunks/micromark-util-decode-numeric-character-reference@2.0.2","vendor-chunks/micromark-util-combine-extensions@2.0.1","vendor-chunks/micromark-util-classify-character@2.0.1","vendor-chunks/micromark-util-chunked@2.0.1","vendor-chunks/micromark-util-character@2.1.1","vendor-chunks/micromark-factory-whitespace@2.0.1","vendor-chunks/micromark-factory-title@2.0.1","vendor-chunks/micromark-factory-space@2.0.1","vendor-chunks/micromark-factory-label@2.0.1","vendor-chunks/micromark-factory-destination@2.0.1","vendor-chunks/mdast-util-to-string@4.0.0","vendor-chunks/mdast-util-from-markdown@2.0.2","vendor-chunks/is-plain-obj@4.1.0","vendor-chunks/html-url-attributes@3.0.1","vendor-chunks/hast-util-whitespace@3.0.0","vendor-chunks/hast-util-to-jsx-runtime@2.3.6","vendor-chunks/estree-util-is-identifier-name@3.0.0","vendor-chunks/devlop@1.1.0","vendor-chunks/dequal@2.0.3","vendor-chunks/decode-named-character-reference@1.2.0","vendor-chunks/comma-separated-tokens@2.0.3","vendor-chunks/character-entities@2.0.2","vendor-chunks/bail@2.0.2","vendor-chunks/style-to-object@1.0.9","vendor-chunks/ms@2.1.3","vendor-chunks/inline-style-parser@0.2.4","vendor-chunks/extend@3.0.2"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2F%5Bid%5D%2Fpage&page=%2Fchat%2F%5Bid%5D%2Fpage&appPaths=%2Fchat%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fchat%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cdata%5Cstudy%5Cpython%5Crag%5Crag-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdata%5Cstudy%5Cpython%5Crag%5Crag-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();