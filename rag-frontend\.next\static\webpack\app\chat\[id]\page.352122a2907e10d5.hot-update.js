"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./src/components/chat/ChatInput.tsx":
/*!*******************************************!*\
  !*** ./src/components/chat/ChatInput.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInput: () => (/* binding */ ChatInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Paperclip,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Paperclip,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInput auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ChatInput(param) {\n    let { onSend, disabled = false, placeholder = \"输入消息...\" } = param;\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleSend = ()=>{\n        if (message.trim() && !disabled) {\n            onSend(message.trim());\n            setMessage('');\n            // 重置textarea高度\n            if (textareaRef.current) {\n                textareaRef.current.style.height = 'auto';\n            }\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleInputChange = (e)=>{\n        setMessage(e.target.value);\n        // 自动调整textarea高度\n        const textarea = e.target;\n        textarea.style.height = 'auto';\n        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white border border-gray-200 rounded-3xl shadow-sm hover:shadow-md transition-shadow duration-200 focus-within:border-gray-300 focus-within:shadow-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-end px-4 py-3 gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            disabled: disabled,\n                            className: \"flex-shrink-0 p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-h-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: message,\n                                onChange: handleInputChange,\n                                onKeyDown: handleKeyDown,\n                                placeholder: placeholder,\n                                disabled: disabled,\n                                className: \"w-full resize-none bg-transparent border-0 outline-none text-gray-900 placeholder-gray-500 text-base leading-6 min-h-[24px] max-h-[200px] py-0\",\n                                rows: 1,\n                                style: {\n                                    height: 'auto'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: handleSend,\n                            disabled: !message.trim() || disabled,\n                            className: \"flex-shrink-0 p-2 bg-gray-900 text-white rounded-xl hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-400 mt-2 px-1\",\n                children: \"按 Enter 发送消息，Shift + Enter 换行\"\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInput, \"nSZtx3BjYZl96sTgPGtyk23sYqk=\");\n_c = ChatInput;\nvar _c;\n$RefreshReg$(_c, \"ChatInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/ChatInput.tsx\n"));

/***/ })

});