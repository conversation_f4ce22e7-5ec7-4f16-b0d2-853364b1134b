'use client';

import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { chatApi } from '@/lib/api';
import { Conversation, Message } from '@/lib/types';
import { ChatMessages } from '@/components/chat/ChatMessages';
import { ChatInput } from '@/components/chat/ChatInput';
import { useChatStore } from '@/lib/store';

export default function ChatPage() {
  const params = useParams();
  const conversationId = params.id as string;

  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const { setConversationTitle } = useChatStore();

  useEffect(() => {
    if (!conversationId) return;

    const loadConversationAndMessages = async () => {
      try {
        setLoading(true);

        // 通过历史接口同时获取对话信息和消息
        const response = await chatApi.getConversationHistory(conversationId);

        if (response.success && response.data) {
          setConversation(response.data.conversation);
          setConversationTitle(response.data.conversation.title);

          // 将qa_pairs转换为Message格式
          const messages: Message[] = [];
          response.data.qa_pairs.forEach(qa => {
            // 添加用户消息
            messages.push({
              id: qa.id + '_user',
              content: qa.query,
              role: 'user',
              timestamp: qa.created_at,
              conversationId: conversationId
            });
            // 添加助手回复
            messages.push({
              id: qa.id + '_assistant',
              content: qa.answer,
              role: 'assistant',
              timestamp: qa.created_at,
              conversationId: conversationId,
              metadata: qa.metadata
            });
          });

          // 按时间排序消息
          messages.sort((a, b) => {
            const timeA = typeof a.timestamp === 'string' ? new Date(a.timestamp) : a.timestamp;
            const timeB = typeof b.timestamp === 'string' ? new Date(b.timestamp) : b.timestamp;
            return timeA.getTime() - timeB.getTime();
          });

          setMessages(messages);
        }
      } catch (error) {
        console.error('加载对话失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadConversationAndMessages();

    return () => {
      setConversationTitle(null);
    };
  }, [conversationId, setConversationTitle]);

  // 处理初始消息（从sessionStorage获取）
  useEffect(() => {
    if (conversation && messages.length === 0) {
      const initialMessage = sessionStorage.getItem(`initialMessage_${conversationId}`);
      if (initialMessage) {
        // 发送初始消息
        handleSendMessage(initialMessage).then(() => {
          sessionStorage.removeItem(`initialMessage_${conversationId}`);
        });
      }
    }
  }, [conversation, messages.length, conversationId]);

  const handleSendMessage = async (message: string) => {
    if (!message.trim() || !conversation) return;

    // 添加用户消息到界面
    const userMessage: Message = {
      id: Date.now().toString(),
      content: message,
      role: 'user',
      timestamp: new Date(),
      conversationId: conversation.id
    };
    setMessages(prev => [...prev, userMessage]);

    // 创建一个临时的助手消息用于流式更新
    const tempAssistantId = 'temp_' + Date.now();
    const assistantMessage: Message = {
      id: tempAssistantId,
      content: '',
      role: 'assistant',
      timestamp: new Date(),
      conversationId: conversation.id
    };
    setMessages(prev => [...prev, assistantMessage]);

    try {
      await chatApi.sendMessageStream(conversationId, {
        message: message
      }, (chunk) => {
        switch (chunk.type) {
          case 'start':
            // 流式开始，可以显示打字指示器
            break;

          case 'content':
            // 更新助手消息内容（直接替换，因为后端返回的是完整文本）
            if (chunk.content) {
              setMessages(prev => prev.map(msg =>
                msg.id === tempAssistantId
                  ? { ...msg, content: chunk.content || '' }
                  : msg
              ));
            }
            break;

          case 'done':
            // 流式完成，更新消息ID和元数据
            setMessages(prev => prev.map(msg =>
              msg.id === tempAssistantId
                ? {
                  ...msg,
                  id: chunk.qa_pair_id || tempAssistantId,
                  metadata: chunk.metadata
                }
                : msg
            ));
            break;

          case 'error':
            // 处理错误
            console.error('流式响应错误:', chunk.error);
            setMessages(prev => prev.map(msg =>
              msg.id === tempAssistantId
                ? { ...msg, content: '抱歉，发生了错误：' + chunk.error }
                : msg
            ));
            break;
        }
      });
    } catch (error) {
      console.error('发送消息失败:', error);
      // 更新错误消息
      setMessages(prev => prev.map(msg =>
        msg.id === tempAssistantId
          ? { ...msg, content: '抱歉，发送消息失败，请重试。' }
          : msg
      ));
    }
  };



  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    );
  }

  if (!conversation) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">对话不存在</h2>
          <p className="text-gray-500">请检查链接是否正确</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* 消息列表 */}
      <div className="flex-1 max-w-3xl mx-auto">
        <ChatMessages messages={messages} />
      </div>

      {/* 输入框 */}
      <div className="sticky bottom-0 bg-white/95 backdrop-blur-md border-t border-gray-100 px-4 py-4 flex-shrink-0">
        <div className="max-w-4xl mx-auto">
          <ChatInput onSend={handleSendMessage} />
        </div>
      </div>
    </div>
  );
}
