'use client';

import { useState, useRef, KeyboardEvent } from 'react';
import { Send, Paperclip, Mic } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ChatInputProps {
  onSend: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export function ChatInput({ 
  onSend, 
  disabled = false, 
  placeholder = "输入消息..." 
}: ChatInputProps) {
  const [message, setMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSend = () => {
    if (message.trim() && !disabled) {
      onSend(message.trim());
      setMessage('');
      // 重置textarea高度
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    
    // 自动调整textarea高度
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  };

  return (
    <div className="flex items-end gap-3 p-4 bg-white  border-gray-200">
      {/* 附件按钮 */}
      <Button
        variant="ghost"
        size="sm"
        className="text-gray-400 hover:text-gray-600 p-2"
        disabled={disabled}
      >
        <Paperclip size={18} />
      </Button>

      {/* 输入框容器 */}
      <div className="flex-1 relative">
        <textarea
          ref={textareaRef}
          value={message}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className="w-full resize-none border border-gray-300 rounded-xl px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-400 min-h-[48px] max-h-[120px]"
          rows={1}
        />
        
        {/* 发送按钮 */}
        <Button
          onClick={handleSend}
          disabled={!message.trim() || disabled}
          className="absolute right-2 bottom-2 p-2 h-8 w-8 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 rounded-lg"
          size="sm"
        >
          <Send size={14} />
        </Button>
      </div>

      {/* 语音按钮 */}
      <Button
        variant="ghost"
        size="sm"
        className="text-gray-400 hover:text-gray-600 p-2"
        disabled={disabled}
      >
        <Mic size={18} />
      </Button>
    </div>
  );
}