'use client';

import { useState, useRef, KeyboardEvent } from 'react';
import { Send, Paperclip } from 'lucide-react';

interface ChatInputProps {
  onSend: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export function ChatInput({ 
  onSend, 
  disabled = false, 
  placeholder = "输入消息..." 
}: ChatInputProps) {
  const [message, setMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSend = () => {
    if (message.trim() && !disabled) {
      onSend(message.trim());
      setMessage('');
      // 重置textarea高度
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    
    // 自动调整textarea高度
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  };

  return (
    <div className="relative">
      {/* Claude风格上下分层输入框 */}
      <div className="relative bg-white border border-gray-200 rounded-2xl transition-all duration-200 focus-within:border-gray-300">
        {/* 上半部分：纯文本输入区域 */}
        <div className="px-4 pt-3 pb-2">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className="w-full resize-none bg-transparent border-0 outline-none text-gray-900 placeholder-gray-400 text-base leading-6 min-h-[48px] max-h-[200px] font-normal"
            rows={2}
            style={{ height: 'auto' }}
          />
        </div>

        {/* 下半部分：功能按钮区域 */}
        <div className="flex items-center justify-between px-4 pb-3 pt-2">
          {/* 左侧：附件按钮 */}
          <div className="flex items-center gap-2">
            <button
              type="button"
              disabled={disabled}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Paperclip size={18} />
            </button>
          </div>

          {/* 右侧：发送按钮 - 匹配网站蓝紫色系 */}
          <button
            type="button"
            onClick={handleSend}
            disabled={!message.trim() || disabled}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              message.trim() && !disabled
                ? 'bg-blue-500 text-white hover:bg-blue-600'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            }`}
          >
            <div className="flex items-center gap-2">
              <Send size={16} />
              <span>发送</span>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
}