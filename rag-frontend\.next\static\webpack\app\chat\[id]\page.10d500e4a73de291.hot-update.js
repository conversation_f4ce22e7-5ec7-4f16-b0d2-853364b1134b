"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./src/components/chat/ChatInput.tsx":
/*!*******************************************!*\
  !*** ./src/components/chat/ChatInput.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInput: () => (/* binding */ ChatInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Paperclip,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Paperclip,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.536.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInput auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ChatInput(param) {\n    let { onSend, disabled = false, placeholder = \"输入消息...\" } = param;\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleSend = ()=>{\n        if (message.trim() && !disabled) {\n            onSend(message.trim());\n            setMessage('');\n            // 重置textarea高度\n            if (textareaRef.current) {\n                textareaRef.current.style.height = 'auto';\n            }\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleInputChange = (e)=>{\n        setMessage(e.target.value);\n        // 自动调整textarea高度\n        const textarea = e.target;\n        textarea.style.height = 'auto';\n        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white border border-gray-200 rounded-2xl transition-all duration-200 focus-within:border-blue-400 focus-within:ring-2 focus-within:ring-blue-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-end px-5 py-4 gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            disabled: disabled,\n                            className: \"flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-h-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: message,\n                                onChange: handleInputChange,\n                                onKeyDown: handleKeyDown,\n                                placeholder: placeholder,\n                                disabled: disabled,\n                                className: \"w-full resize-none bg-transparent border-0 outline-none text-gray-900 placeholder-gray-400 text-[15px] leading-6 min-h-[24px] max-h-[200px] py-0 font-normal\",\n                                rows: 1,\n                                style: {\n                                    height: 'auto'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: handleSend,\n                            disabled: !message.trim() || disabled,\n                            className: \"flex-shrink-0 p-2.5 rounded-full transition-all duration-200 \".concat(message.trim() && !disabled ? 'bg-blue-500 text-white hover:bg-blue-600 hover:scale-105 shadow-sm' : 'bg-gray-100 text-gray-400 cursor-not-allowed'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                size: 16,\n                                className: message.trim() && !disabled ? 'translate-x-0.5' : ''\n                            }, void 0, false, {\n                                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-400 mt-3 text-center\",\n                children: [\n                    \"按 \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                        className: \"px-1.5 py-0.5 bg-gray-100 rounded text-xs\",\n                        children: \"Enter\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    \" 发送，\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                        className: \"px-1.5 py-0.5 bg-gray-100 rounded text-xs\",\n                        children: \"Shift + Enter\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    \" 换行\"\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\data\\\\study\\\\python\\\\rag\\\\rag-frontend\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInput, \"nSZtx3BjYZl96sTgPGtyk23sYqk=\");\n_c = ChatInput;\nvar _c;\n$RefreshReg$(_c, \"ChatInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/ChatInput.tsx\n"));

/***/ })

});